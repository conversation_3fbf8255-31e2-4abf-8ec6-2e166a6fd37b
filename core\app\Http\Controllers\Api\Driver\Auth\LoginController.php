<?php

namespace App\Http\Controllers\Api\Driver\Auth;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Lib\SocialLogin;
use App\Models\UserLogin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    /**
     * Handle driver login request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = $this->validateLogin($request);
        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $credentials = [
            'email' => $request->input('email'),
            'password' => $request->input('password')
        ];

        if (!Auth::guard('driver')->attempt(array_merge($credentials, ['is_deleted' => Status::NO]))) {
            $response[] = 'The provided credentials can not match our record';
            return apiResponse("invalid_credential", "error", $response);
        }

        $driver = Auth::guard('driver')->user();
        $tokenResult = $driver->createToken('driver_token', ['driver'])->plainTextToken;
        $this->authenticated($request, $driver);
        $response[] = 'Login Successful';

        return apiResponse("login_success", "success", $response, [
            'driver'       => $driver,
            'access_token' => $tokenResult,
            'token_type'   => 'Bearer'
        ]);
    }

    /**
     * Validate the login request
     *
     * @param Request $request
     * @return \Illuminate\Validation\Validator
     */
    protected function validateLogin(Request $request)
    {
        $rules = [
            'email' => 'required|string|email',
            'password' => 'required',
        ];

        return Validator::make($request->all(), $rules);
    }

    /**
     * Handle driver logout
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        auth()->user()->tokens()->delete();

        $notify[] = 'Logout Successful';
        return apiResponse("logout", "success", $notify);
    }

    /**
     * Handle post-authentication tasks
     *
     * @param Request $request
     * @param $driver
     */
    public function authenticated(Request $request, $driver)
    {
        $driver->tv = $driver->ts == Status::VERIFIED ? Status::UNVERIFIED : Status::VERIFIED;
        $driver->save();

        $ip        = getRealIP();
        $exist     = UserLogin::where('user_ip', $ip)->where('driver_id', $driver->id)->first();
        $driverLogin = new UserLogin();

        if ($exist) {
            $driverLogin->longitude    = $exist->longitude;
            $driverLogin->latitude     = $exist->latitude;
            $driverLogin->city         = $exist->city;
            $driverLogin->country_code = $exist->country_code;
            $driverLogin->country      = $exist->country;
        } else {
            $info                    = json_decode(json_encode(getIpInfo()), true);
            $driverLogin->longitude    = @implode(',', $info['long']);
            $driverLogin->latitude     = @implode(',', $info['lat']);
            $driverLogin->city         = @implode(',', $info['city']);
            $driverLogin->country_code = @implode(',', $info['code']);
            $driverLogin->country      = @implode(',', $info['country']);
        }

        $driverAgent            = osBrowser();
        $driverLogin->driver_id = $driver->id;
        $driverLogin->user_ip   = $ip;

        $driverLogin->browser = @$driverAgent['browser'];
        $driverLogin->os      = @$driverAgent['os_platform'];
        $driverLogin->save();
    }

    /**
     * Handle social login
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function socialLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider' => 'required|in:google,apple',
            'token'    => 'required',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $socialLogin = new SocialLogin('driver',$request->provider);
        return $socialLogin->login();
    }

    /**
     * Get the guard to be used during authentication
     *
     * @return \Illuminate\Contracts\Auth\Guard
     */
    protected function guard()
    {
        return Auth::guard('driver');
    }
}
