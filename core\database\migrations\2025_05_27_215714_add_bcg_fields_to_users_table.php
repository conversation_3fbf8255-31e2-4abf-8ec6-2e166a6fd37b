<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add BCG employee fields only if they don't exist
            if (!Schema::hasColumn('users', 'employ_type')) {
                $table->string('employ_type')->nullable()->after('email');
            }
            if (!Schema::hasColumn('users', 'full_name')) {
                $table->string('full_name')->nullable()->after('employ_type');
            }
            if (!Schema::hasColumn('users', 'employ_id')) {
                $table->string('employ_id')->nullable()->after('full_name');
            }
            if (!Schema::hasColumn('users', 'phone')) {
                $table->string('phone', 20)->nullable()->after('employ_id');
            }

            // Make existing fields nullable for simplified registration
            $table->string('firstname')->nullable()->change();
            $table->string('lastname')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove BCG employee fields if they exist
            if (Schema::hasColumn('users', 'employ_type')) {
                $table->dropColumn('employ_type');
            }
            if (Schema::hasColumn('users', 'full_name')) {
                $table->dropColumn('full_name');
            }
            if (Schema::hasColumn('users', 'employ_id')) {
                $table->dropColumn('employ_id');
            }
            if (Schema::hasColumn('users', 'phone')) {
                $table->dropColumn('phone');
            }

            // Revert firstname and lastname to required (if needed)
            $table->string('firstname')->nullable(false)->change();
            $table->string('lastname')->nullable(false)->change();
        });
    }
};
