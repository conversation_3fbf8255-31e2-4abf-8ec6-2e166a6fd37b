<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver API Documentation</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --success-color: #059669;
            --danger-color: #dc2626;
            --warning-color: #d97706;
            --info-color: #0284c7;
            --dark-color: #1f2937;
            --light-color: #f3f4f6;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: var(--dark-color);
            background: #f8fafc;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 2rem;
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin: 0;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
        }

        h2 {
            color: var(--secondary-color);
            margin-top: 2rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .endpoint {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
            border-left: 4px solid var(--primary-color);
        }

        .method {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .method.get {
            background: #dbeafe;
            color: #1e40af;
        }

        .method.post {
            background: #dcfce7;
            color: #166534;
        }

        .method.put {
            background: #fef3c7;
            color: #92400e;
        }

        .method.delete {
            background: #fee2e2;
            color: #991b1b;
        }

        .url {
            font-family: 'Fira Code', monospace;
            background: var(--light-color);
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            display: inline-block;
            margin: 0.5rem 0;
        }

        .params {
            margin-top: 1rem;
            padding: 1rem;
            background: var(--light-color);
            border-radius: 0.5rem;
        }

        .param {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: white;
            border-radius: 0.25rem;
        }

        .param-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .response {
            margin-top: 1rem;
            padding: 1rem;
            background: #f0fdf4;
            border-radius: 0.5rem;
            border: 1px solid #bbf7d0;
        }

        .flow-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .flow-step {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #fff;
        }

        .flow-step h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }

        .flow-step ul {
            margin: 0;
            padding-left: 20px;
        }

        .flow-step .note {
            font-style: italic;
            color: #666;
            margin-top: 10px;
        }

        .nav {
            position: sticky;
            top: 0;
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .nav a {
            color: var(--primary-color);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: all 0.2s;
        }

        .nav a:hover {
            background: var(--light-color);
        }

        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            font-family: 'Fira Code', monospace;
            margin: 1rem 0;
        }

        .required {
            color: var(--danger-color);
            font-size: 0.875rem;
            margin-left: 0.5rem;
        }

        .auth-info {
            background: #fef3c7;
            border: 1px solid #fcd34d;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Driver API Documentation</h1>
            <p>Complete API documentation for the Driver Management System</p>
        </div>

        <div class="nav">
            <ul>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#profile">Profile Management</a></li>
                <li><a href="#verification">Verification</a></li>
                <li><a href="#rides">Ride Management</a></li>
                <li><a href="#payments">Payments</a></li>
                <li><a href="#notifications">Notifications</a></li>
            </ul>
        </div>

        <div class="auth-info">
            <h3>Required Headers</h3>
            <div class="code-block">
                Content-Type: application/json<br>
                Accept: application/json<br>
                dev-token: $2y$12$mEVBW3QASB5HMBv8igls3ejh6zw2A0Xb480HWAmYq6BY9xEifyBjG
            </div>
        </div>

        <section id="api-flow">
            <h2>API Flow Overview</h2>
            <div class="flow-section">
                <h3>1. Initial Setup & Authentication</h3>
                <div class="flow-step">
                    <h4>Step 1: Registration</h4>
                    <ul>
                        <li>Call <code>POST /api/driver/register</code> with basic details</li>
                        <li>Receive access token and driver information</li>
                        <li>Save the access token for subsequent requests</li>
                    </ul>
                    <div class="note">Note: After registration, you'll need to complete profile setup before accessing
                        other features.</div>
                </div>

                <div class="flow-step">
                    <h4>Step 2: Profile Completion</h4>
                    <ul>
                        <li>Call <code>POST /api/driver/driver-data-submit</code> to complete profile</li>
                        <li>Required fields: username, mobile, address, etc.</li>
                        <li>This step is mandatory before proceeding to verification</li>
                    </ul>
                </div>

                <h3>2. Verification Process</h3>
                <div class="flow-step">
                    <h4>Step 3: Driver Verification</h4>
                    <ul>
                        <li>Get verification form: <code>GET /api/driver/driver-verification</code></li>
                        <li>Submit documents: <code>POST /api/driver/driver-verification</code></li>
                        <li>Wait for admin approval</li>
                    </ul>
                </div>

                <div class="flow-step">
                    <h4>Step 4: Vehicle Verification</h4>
                    <ul>
                        <li>Get vehicle form: <code>GET /api/driver/vehicle-verification</code></li>
                        <li>Submit vehicle details: <code>POST /api/driver/vehicle-verification</code></li>
                        <li>Required: vehicle images, registration number, etc.</li>
                        <li>Wait for admin approval</li>
                    </ul>
                </div>

                <h3>3. Going Online & Ride Management</h3>
                <div class="flow-step">
                    <h4>Step 5: Going Online</h4>
                    <ul>
                        <li>Update online status: <code>POST /api/driver/online-status</code></li>
                        <li>Required: latitude and longitude</li>
                        <li>System will check if you're in an active zone</li>
                    </ul>
                </div>

                <div class="flow-step">
                    <h4>Step 6: Ride Management</h4>
                    <ul>
                        <li>Receive ride requests automatically when online</li>
                        <li>Start ride: <code>POST /api/driver/rides/start/{id}</code></li>
                        <li>Update location: <code>POST /api/driver/rides/live-location/{id}</code></li>
                        <li>End ride: <code>POST /api/driver/rides/end/{id}</code></li>
                        <li>Confirm cash payment: <code>POST /api/driver/rides/received-cash-payment/{id}</code></li>
                    </ul>
                </div>

                <h3>4. Payment & Earnings</h3>
                <div class="flow-step">
                    <h4>Step 7: Payment Management</h4>
                    <ul>
                        <li>View payment history: <code>GET /api/driver/payment/history</code></li>
                        <li>View transactions: <code>GET /api/driver/transactions</code></li>
                        <li>Request withdrawal: <code>POST /api/driver/withdraw-request</code></li>
                        <li>Confirm withdrawal: <code>POST /api/driver/withdraw-request/confirm</code></li>
                    </ul>
                </div>

                <h3>5. Additional Features</h3>
                <div class="flow-step">
                    <h4>Step 8: Profile & Settings</h4>
                    <ul>
                        <li>Update profile: <code>POST /api/driver/profile-setting</code></li>
                        <li>Change password: <code>POST /api/driver/change-password</code></li>
                        <li>Get driver details: <code>GET /api/driver/driver-details</code></li>
                        <li>Enable 2FA: <code>POST /api/driver/twofactor/enable</code></li>
                        <li>Save device token: <code>POST /api/driver/save-device-token</code></li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="authentication">
            <h2>Authentication APIs</h2>

            <div class="endpoint">
                <h3>Driver Registration</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/register</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">firstname</span> - Driver's first name <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">email</span> - Valid email address (must be unique)
                            <span class="required">*</span>
                        </li>
                        <li class="param"><span class="param-name">phone</span> - Phone number (must be unique) <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">country_code</span> - Country code (max 10
                            characters) <span class="required">*</span></li>
                        <li class="param"><span class="param-name">password</span> - Secure password <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">password_confirmation</span> - Password confirmation
                            <span class="required">*</span>
                        </li>
                        <li class="param"><span class="param-name">agree</span> - Terms agreement (if required by system
                            settings)</li>
                    </ul>
                </div>
                <div class="code-block">
                    {
                    "firstname": "John",
                    "email": "<EMAIL>",
                    "phone": "1234567890",
                    "country_code": "US",
                    "password": "SecurePassword123!",
                    "password_confirmation": "SecurePassword123!",
                    "agree": 1
                    }
                </div>
                <div class="response">
                    <p>Success Response (200):</p>
                    <div class="code-block">
                        {
                        "success": true,
                        "message": "Registration successful",
                        "data": {
                        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        "driver": {
                        "id": 1,
                        "firstname": "John",
                        "lastname": "",
                        "email": "<EMAIL>",
                        "phone": "1234567890",
                        "country_code": "US",
                        "profile_complete": 0,
                        "ev": 0,
                        "sv": 0,
                        "dv": 0,
                        "vv": 0,
                        "created_at": "2024-01-01T00:00:00.000000Z"
                        },
                        "token_type": "Bearer",
                        "image_path": "/path/to/driver/images"
                        }
                        }
                    </div>
                    <p>Error Response (422):</p>
                    <div class="code-block">
                        {
                        "success": false,
                        "message": "validation_error",
                        "data": [
                        "The email has already been taken.",
                        "The phone has already been taken."
                        ]
                        }
                    </div>
                </div>
                <div class="auth-info">
                    <h4>Important Notes:</h4>
                    <ul>
                        <li>Email and phone must be unique across all drivers</li>
                        <li>Password requirements depend on system security settings</li>
                        <li>Country code should be ISO country code (e.g., "US", "UK", "IN")</li>
                        <li>After registration, complete profile setup is required</li>
                        <li>Driver registration can be disabled by admin</li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Driver Login</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/login</p>
                <div class="params">
                    <p>Required Parameters (can be sent in query string or request body):</p>
                    <ul>
                        <li class="param"><span class="param-name">email</span> - Registered email address <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">password</span> - Account password <span
                                class="required">*</span></li>
                    </ul>
                </div>
                <div class="response">
                    <p>Response includes:</p>
                    <ul>
                        <li>Access token</li>
                        <li>Driver information</li>
                        <li>Token type</li>
                    </ul>
                </div>
                <div class="note">
                    <p>Note: The login credentials can be sent either in the query string or request body.</p>
                </div>
                <div class="example">
                    <p>Example URL:</p>
                    <ul>
                        <li>Query String: <code>/api/driver/login?email=<EMAIL>&password=yourpassword</code>
                        </li>
                    </ul>
                    <p>Example Request Body:</p>
                    <pre>
{
    "email": "<EMAIL>",
    "password": "yourpassword"
}
                    </pre>
                </div>
            </div>

            <div class="endpoint">
                <h3>Social Login</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/social-login</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">provider</span> - Social provider (google/apple)
                            <span class="required">*</span>
                        </li>
                        <li class="param"><span class="param-name">token</span> - Social login token <span
                                class="required">*</span></li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="profile">
            <h2>Profile Management APIs</h2>

            <div class="endpoint">
                <h3>Submit Driver Data</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/driver-data-submit</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">address</span> - Driver's address <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">zip</span> - ZIP code <span class="required">*</span>
                        </li>
                        <li class="param"><span class="param-name">country</span> - Country name <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">mobile_code</span> - Country dial code <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">zone</span> - Operating zone ID <span
                                class="required">*</span></li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Update Profile</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/profile-setting</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">firstname</span> - First name</li>
                        <li class="param"><span class="param-name">lastname</span> - Last name</li>
                        <li class="param"><span class="param-name">email</span> - Email address</li>
                        <li class="param"><span class="param-name">mobile</span> - Mobile number</li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Change Password</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/change-password</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">current_password</span> - Current password <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">password</span> - New password <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">password_confirmation</span> - Confirm new password
                            <span class="required">*</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Get Driver Details</h3>
                <span class="method get">GET</span>
                <p class="url">/api/driver/driver-details</p>
                <div class="auth-info">
                    <p><strong>Authentication Required:</strong> Bearer Token</p>
                </div>
                <div class="params">
                    <p>No Parameters Required</p>
                    <p>This endpoint retrieves comprehensive driver information including:</p>
                    <ul>
                        <li>Personal information (name, email, phone, address)</li>
                        <li>Professional info (balance, status, ratings)</li>
                        <li>Verification status (email, mobile, documents, vehicle)</li>
                        <li>Service and zone information</li>
                        <li>Complete vehicle details</li>
                        <li>Statistics (rides, earnings, completion rate)</li>
                        <li>Recent reviews (last 10)</li>
                        <li>Rider rules</li>
                    </ul>
                </div>
                <div class="response">
                    <p>Success Response (200):</p>
                    <div class="code-block">
                        {
                        "success": true,
                        "message": "Driver details retrieved successfully",
                        "data": {
                        "driver": {
                        "id": 1,
                        "personal_info": {
                        "firstname": "John",
                        "lastname": "Doe",
                        "fullname": "John Doe",
                        "username": "john_driver",
                        "email": "<EMAIL>",
                        "mobile": "1234567890",
                        "dial_code": "+1",
                        "mobile_number": "+11234567890",
                        "address": "123 Main St",
                        "city": "New York",
                        "state": "NY",
                        "zip": "10001",
                        "country_name": "United States"
                        },
                        "professional_info": {
                        "balance": "1,250.00",
                        "online_status": 1,
                        "status": 1,
                        "profile_complete": 1,
                        "total_reviews": 45,
                        "avg_rating": "4.75"
                        },
                        "verification_status": {
                        "email_verified": 1,
                        "mobile_verified": 1,
                        "document_verified": 1,
                        "vehicle_verified": 1,
                        "two_factor_enabled": 0
                        },
                        "service": {
                        "id": 1,
                        "name": "Car",
                        "image": "car_icon.png"
                        },
                        "vehicle": {
                        "id": 1,
                        "vehicle_number": "ABC123",
                        "model": {"id": 1, "name": "Camry"},
                        "color": {"id": 1, "name": "White"},
                        "year": {"id": 1, "name": "2022"},
                        "brand": {"id": 1, "name": "Toyota"}
                        },
                        "statistics": {
                        "total_rides": 150,
                        "completed_rides": 145,
                        "total_earnings": "5,250.00",
                        "average_rating": "4.75",
                        "completion_rate": "96.67"
                        }
                        }
                        }
                        }
                    </div>
                </div>
            </div>
        </section>

        <section id="verification">
            <h2>Verification APIs</h2>

            <div class="endpoint">
                <h3>Driver Verification</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/driver-verification</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">verification_documents</span> - Required verification
                            documents <span class="required">*</span></li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Vehicle Verification</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/vehicle-verification</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">brand_id</span> - Vehicle brand ID <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">model_id</span> - Vehicle model ID <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">color_id</span> - Vehicle color ID <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">year_id</span> - Vehicle year ID <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">service_id</span> - Service type ID <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">vehicle_number</span> - Vehicle registration number
                            <span class="required">*</span>
                        </li>
                        <li class="param"><span class="param-name">image</span> - Vehicle image <span
                                class="required">*</span></li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="rides">
            <h2>Ride Management APIs</h2>

            <div class="endpoint">
                <h3>Online Status Update</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/online-status</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">lat</span> - Current latitude <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">long</span> - Current longitude <span
                                class="required">*</span></li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Ride Management</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/rides/{action}</p>
                <div class="params">
                    <p>Available Actions:</p>
                    <ul>
                        <li class="param"><span class="param-name">start/{id}</span> - Start a ride</li>
                        <li class="param"><span class="param-name">end/{id}</span> - End a ride</li>
                        <li class="param"><span class="param-name">received-cash-payment/{id}</span> - Confirm cash
                            payment</li>
                        <li class="param"><span class="param-name">live-location/{id}</span> - Update live location</li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Get Ride List</h3>
                <span class="method get">GET</span>
                <p class="url">/api/driver/rides/list</p>
                <div class="response">
                    <p>Returns paginated list of rides with:</p>
                    <ul>
                        <li>Ride details</li>
                        <li>User information</li>
                        <li>Payment status</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="payments">
            <h2>Payment APIs</h2>

            <div class="endpoint">
                <h3>Payment History</h3>
                <span class="method get">GET</span>
                <p class="url">/api/driver/payment/history</p>
                <div class="response">
                    <p>Returns paginated list of payment history with:</p>
                    <ul>
                        <li>Payment details</li>
                        <li>Ride information</li>
                        <li>Rider information</li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Withdraw Request</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/withdraw-request</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">method_id</span> - Withdrawal method ID <span
                                class="required">*</span></li>
                        <li class="param"><span class="param-name">amount</span> - Withdrawal amount <span
                                class="required">*</span></li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Transaction History</h3>
                <span class="method get">GET</span>
                <p class="url">/api/driver/transactions</p>
                <div class="params">
                    <p>Optional Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">search</span> - Search by transaction ID</li>
                        <li class="param"><span class="param-name">type</span> - Transaction type (plus/minus)</li>
                        <li class="param"><span class="param-name">remark</span> - Transaction remark</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="notifications">
            <h2>Notification APIs</h2>

            <div class="endpoint">
                <h3>Save Device Token</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/save-device-token</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">token</span> - Device token for push notifications
                            <span class="required">*</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="endpoint">
                <h3>Push Notifications</h3>
                <span class="method get">GET</span>
                <p class="url">/api/driver/push-notifications</p>
                <div class="response">
                    <p>Returns paginated list of push notifications</p>
                </div>
            </div>

            <div class="endpoint">
                <h3>Mark Notification as Read</h3>
                <span class="method post">POST</span>
                <p class="url">/api/driver/push-notifications/read/{id}</p>
                <div class="params">
                    <p>Required Parameters:</p>
                    <ul>
                        <li class="param"><span class="param-name">id</span> - Notification ID <span
                                class="required">*</span></li>
                    </ul>
                </div>
            </div>
        </section>
    </div>
</body>

</html>