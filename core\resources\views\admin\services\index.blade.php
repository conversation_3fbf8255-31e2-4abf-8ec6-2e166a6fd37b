@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-12">
            <x-admin.ui.card>
                <x-admin.ui.card.body :paddingZero=true>
                    <x-admin.ui.table.layout searchPlaceholder="Search service" :renderExportButton="false">
                        <x-admin.ui.table>
                            <x-admin.ui.table.header>
                                <tr>
                                    <th>@lang('Service')</th>
                                    <th>@lang('Half Hour Fare')</th>
                                    <th>@lang('Full Day Fare')</th>
                                    <th>@lang('Cancellation Charges')</th>
                                    <th>@lang('Passengers')</th>
                                    <th>@lang('Commission')</th>
                                    <th>@lang('Status')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </x-admin.ui.table.header>
                            <x-admin.ui.table.body>
                                @forelse($services as $service)
                                    <tr>
                                        <td>
                                            <div class="flex-thumb-wrapper gap-1">
                                                <div class="thumb">
                                                    <img src="{{ imageGet('service', $service->image) }}">
                                                </div>
                                                <span>
                                                    {{ __($service->name) }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold">
                                                {{ showAmount($service->half_hour_fare) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold">
                                                {{ showAmount($service->full_day_fare) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold">
                                                {{ showAmount($service->cancellation_charges) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold">
                                                {{ $service->no_of_passengers }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold">
                                                {{ getAmount($service->city_fare_commission) }}%
                                            </span>
                                        </td>
                                        <td>
                                            <x-admin.other.status_switch :status="$service->status" :action="route('admin.service.status', $service->id)"
                                                title="service" />
                                        </td>
                                        <td>
                                            <x-admin.ui.btn.edit tag="button" :data-image="imageGet('service', $service->image)" :data-resource="$service" />
                                        </td>
                                    </tr>
                                @empty
                                    <x-admin.ui.table.empty_message />
                                @endforelse
                            </x-admin.ui.table.body>
                        </x-admin.ui.table>
                        @if ($services->hasPages())
                            <x-admin.ui.table.footer>
                                {{ paginateLinks($services) }}
                            </x-admin.ui.table.footer>
                        @endif
                    </x-admin.ui.table.layout>
                </x-admin.ui.card.body>
            </x-admin.ui.card>
        </div>
    </div>

    <x-admin.ui.modal id="modal" class="modal-xl">
        <x-admin.ui.modal.header>
            <h4 class="modal-title"></h4>
            <button type="button" class="btn-close close" data-bs-dismiss="modal" aria-label="Close">
                <i class="las la-times"></i>
            </button>
        </x-admin.ui.modal.header>
        <x-admin.ui.modal.body>
            <form action="{{ route('admin.service.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-group">
                    <label>@lang('Image')</label>
                    <x-image-uploader type="service" />
                </div>
                <div class="form-group">
                    <label>@lang('Name')</label>
                    <input class="form-control" name="name" type="text" required value="{{ old('name') }}">
                </div>

                {{--
                    VISIBLE FIELDS: The following NEW fields are visible and editable:
                    - half_hour_fare
                    - full_day_fare
                    - cancellation_charges
                    - no_of_passengers
                    - city_fare_commission

                    HIDDEN FIELDS: The following OLD fields are hidden and set to null in controller:
                    - city_min_fare, city_max_fare, city_recommend_fare
                    - intercity_min_fare, intercity_max_fare, intercity_recommend_fare, intercity_fare_commission
                --}}
                <div class="row mb-3">
                    <div class="col-lg-3">
                        <div class="form-group">
                            <label>@lang('Half Hour Fare')</label>
                            <div class="input-group input--group">
                                <input class="form-control" name="half_hour_fare" type="number" step="any" required
                                    value="{{ old('half_hour_fare') }}">
                                <span class="input-group-text">{{ gs('cur_text') }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="form-group">
                            <label>@lang('Full Day Fare')</label>
                            <div class="input-group input--group">
                                <input class="form-control" name="full_day_fare" type="number" step="any" required
                                    value="{{ old('full_day_fare') }}">
                                <span class="input-group-text">{{ gs('cur_text') }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="form-group">
                            <label>@lang('Cancellation Charges')</label>
                            <div class="input-group input--group">
                                <input class="form-control" name="cancellation_charges" type="number" step="any" required
                                    value="{{ old('cancellation_charges') }}">
                                <span class="input-group-text">{{ gs('cur_text') }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="form-group">
                            <label>@lang('Number of Passengers')</label>
                            <input class="form-control" name="no_of_passengers" type="number" required
                                value="{{ old('no_of_passengers') }}">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label>@lang('Commission')</label>
                            <div class="input-group input--group">
                                <input class="form-control" name="city_fare_commission" type="number" step="any" required
                                    value="{{ old('city_fare_commission') }}">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <x-admin.ui.btn.modal />
                </div>
            </form>
        </x-admin.ui.modal.body>
    </x-admin.ui.modal>
@endsection

@push('script')
    <script>
        (function($) {
            "use strict";
            const $modal = $("#modal");

            $(".edit-btn").on('click', function(e) {
                const data = $(this).data('resource');
                const imagePath = $(this).data('image');
                const action = "{{ route('admin.service.store', ':id') }}";
                $("input[name='name']").val(data.name);
                $("input[name='half_hour_fare']").val(getAmount(data.half_hour_fare));
                $("input[name='full_day_fare']").val(getAmount(data.full_day_fare));
                $("input[name='cancellation_charges']").val(getAmount(data.cancellation_charges));
                $("input[name='no_of_passengers']").val(data.no_of_passengers);
                $("input[name='city_fare_commission']").val(getAmount(data.city_fare_commission, 2));
                $modal.find(".modal-title").text("@lang('Edit Service')");
                $modal.find(".image-upload img").attr('src', imagePath);
                $modal.find(".image-upload [type=file]").attr('required', false);
                $modal.find('form').attr('action', action.replace(':id', data.id));
                $modal.modal("show");
            });

            $(".add-btn").on('click', function(e) {
                const action = "{{ route('admin.service.store') }}";
                $modal.find(".modal-title").text("@lang('Add Service')");
                $modal.find('form').trigger('reset');
                $modal.find('form').attr('action', action);
                $modal.find(".image-upload img").attr('src', "{{ asset('assets/images/drag-and-drop.png') }}");
                $modal.find(".image-upload [type=file]").attr('required', true);
                $modal.modal("show");
            });
        })(jQuery);
    </script>
@endpush


@push('modal')
    <x-confirmation-modal />
@endpush

@push('breadcrumb-plugins')
    <x-admin.ui.btn.add tag="button" />
@endpush


@push('style')
    <style>
        .divider-title {
            position: relative;
            text-align: center;
            width: max-content;
            margin: 0 auto;
        }

        .divider-title::before {
            position: absolute;
            content: '';
            top: 14px;
            left: -90px;
            background: #6b6b6b65;
            height: 2px;
            width: 80px;
        }

        .divider-title::after {
            position: absolute;
            content: '';
            top: 14px;
            right: -90px;
            background: #6b6b6b65;
            height: 2px;
            width: 80px;
        }
    </style>
@endpush
