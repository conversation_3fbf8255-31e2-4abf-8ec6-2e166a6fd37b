<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Hidden fields - will be set to null to avoid calculation conflicts
            $table->decimal('half_hour_fare', 28, 8)->nullable()->after('intercity_fare_commission');
            $table->decimal('full_day_fare', 28, 8)->nullable()->after('half_hour_fare');
            $table->decimal('cancellation_charges', 28, 8)->nullable()->after('full_day_fare');
            $table->integer('no_of_passengers')->nullable()->after('cancellation_charges');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn(['half_hour_fare', 'full_day_fare', 'cancellation_charges', 'no_of_passengers']);
        });
    }
};
