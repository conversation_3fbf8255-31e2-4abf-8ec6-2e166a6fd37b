@extends('admin.layouts.app')
@section('panel')
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">@lang('Ride Details') - {{ $ride->uid }}</h4>
                <div>
                    <a href="{{ route('admin.rides.receipt', $ride->id) }}" class="btn btn--primary btn-sm" target="_blank">
                        <i class="fas fa-download"></i> @lang('Download Receipt')
                    </a>
                    <a href="{{ route('admin.rides.all') }}" class="btn btn--secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> @lang('Back to Rides')
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row responsive-row">
        <div class="col-lg-6">
            <x-admin.ui.card>
                <x-admin.ui.card.header>
                    <h4 class="card-title">@lang('Location & Distance Information')</h4>
                </x-admin.ui.card.header>
                <x-admin.ui.card.body>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Pickup Location')</span>
                            <span> {{ __(@$ride->pickup_location) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Destination')</span>
                            <span> {{ __(@$ride->destination) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Distance')</span>
                            <span class="badge badge--info"> {{ __(@$ride->distance) }} @lang('KM')</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Ride Start Time')</span>
                            @if (@$ride->start_time)
                                <span class="text--info">
                                    {{ showDateTime(@$ride->start_time) }}
                                </span>
                            @else
                                <span>
                                    @lang('Not available')
                                </span>
                            @endif
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Ride End Time')</span>
                            @if (@$ride->end_time)
                                <span class="text--info">
                                    {{ showDateTime(@$ride->end_time) }}
                                </span>
                            @else
                                <span>
                                    @lang('Not available')
                                </span>
                            @endif
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Duration')</span>
                            <span>
                                @lang('Approximately')
                                <span class="text--info"> {{ __(@$ride->duration) }}</span>
                            </span>
                        </li>
                        @if($ride->stops)
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Multiple Stops')</span>
                            <span class="badge badge--info">{{ count(is_array($ride->stops) ? $ride->stops : json_decode($ride->stops, true)) }} @lang('stops')</span>
                        </li>
                        @endif
                    </ul>
                </x-admin.ui.card.body>
            </x-admin.ui.card>
        </div>
        <div class="col-lg-6">
            <x-admin.ui.card>
                <x-admin.ui.card.header>
                    <h4 class="card-title">@lang('Ride Information')</h4>
                </x-admin.ui.card.header>
                <x-admin.ui.card.body>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('UID')</span>
                            <span> {{ __($ride->uid) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Service')</span>
                            <span> {{ __($ride->service->name) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Ride Type')</span>
                            @if ($ride->ride_type == Status::INTER_CITY_RIDE)
                                <span class="badge badge--success"> @lang('Intercity Ride') </span>
                            @else
                                <span class="badge badge--info"> @lang('City Ride') </span>
                            @endif
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Amount')</span>
                            <span> {{ showAmount($ride->amount) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Note')</span>
                            <span>{{ __($ride->note ?? 'N/A') }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Status')</span>
                            <span> @php echo $ride->statusBadge @endphp </span>
                        </li>
                        @if($ride->ride_option)
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Ride Option')</span>
                            <span class="badge badge--{{ $ride->ride_option == 'go_now' ? 'success' : 'warning' }}">
                                {{ ucfirst(str_replace('_', ' ', $ride->ride_option)) }}
                            </span>
                        </li>
                        @endif
                        @if($ride->scheduled_time)
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Scheduled Time')</span>
                            <span class="text--info">{{ showDateTime($ride->scheduled_time) }}</span>
                        </li>
                        @endif
                        @if($ride->estimated_duration)
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Estimated Duration')</span>
                            <span class="text--info">{{ $ride->estimated_duration }} @lang('minutes')</span>
                        </li>
                        @endif
                        @if($ride->case_code)
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('BCG Case Code')</span>
                            <span class="badge badge--success">{{ $ride->case_code }}</span>
                        </li>
                        @endif
                        @if($ride->cycles_count && $ride->fare_per_cycle)
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Billing Cycles')</span>
                            <span class="text--info">{{ $ride->cycles_count }} @lang('cycles') × {{ showAmount($ride->fare_per_cycle) }}</span>
                        </li>
                        @endif
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Number of Passengers')</span>
                            <span class="badge badge--primary">{{ $ride->number_of_passenger ?? 1 }} @lang('passengers')</span>
                        </li>
                    </ul>
                </x-admin.ui.card.body>
            </x-admin.ui.card>
        </div>
        <div class="col-lg-6">
            <x-admin.ui.card>
                <x-admin.ui.card.header>
                    <h4 class="card-title">@lang('Rider Information')</h4>
                </x-admin.ui.card.header>
                <x-admin.ui.card.body>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Name')</span>
                            <span> {{ __(@$ride->user->fullname) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Email')</span>
                            <span> {{ __(@$ride->user->email) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Mobile')</span>
                            <span> {{ __(@$ride->user->mobileNumber) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Total Canceled Ride')</span>
                            <span class=" badge badge--danger">{{ $totalUserCancel }}</span>
                        </li>
                    </ul>
                </x-admin.ui.card.body>
            </x-admin.ui.card>
        </div>
        <div class="col-lg-6">
            <x-admin.ui.card>
                <x-admin.ui.card.header>
                    <h4 class="card-title">@lang('Driver Information')</h4>
                </x-admin.ui.card.header>
                <x-admin.ui.card.body>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Name')</span>
                            <span> {{ __(@$ride->driver->fullname ?? 'No driver available') }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Email')</span>
                            <span> {{ __(@$ride->driver->email ?? 'No driver available') }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Mobile')</span>
                            <span> {{ __(@$ride->driver->mobileNumber ?? 'No driver available') }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Total Canceled Ride')</span>
                            <span class="badge badge--danger">{{ $totalDriverCancel }}</span>
                        </li>
                    </ul>
                </x-admin.ui.card.body>
            </x-admin.ui.card>
        </div>

        <div class="col-lg-6">
            <x-admin.ui.card>
                <x-admin.ui.card.header>
                    <h4 class="card-title">@lang('Payment Information')</h4>
                </x-admin.ui.card.header>
                <x-admin.ui.card.body>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Payment Type')</span>
                            @if ($ride->payment_type == Status::PAYMENT_TYPE_CASH)
                                <span class="badge badge--success">
                                    @lang('Cash Payment')
                                </span>
                            @else
                                <span class="badge badge--info">
                                    @lang('Online Payment')
                                </span>
                            @endif
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Payment Status')</span>
                            <span> @php echo @$ride->paymentStatusType @endphp</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Amount')</span>
                            <span class="text--info">{{ showAmount(@$ride->amount) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Commission Amount')</span>
                            <span class=" text--warning">{{ showAmount(@$ride->commission_amount) }} </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                            <span>@lang('Driver Received')</span>
                            <span class=" text--success">{{ showAmount(@$ride->amount - $ride->commission_amount) }}
                            </span>
                        </li>
                    </ul>
                </x-admin.ui.card.body>
            </x-admin.ui.card>

            @if ($ride->userReview)
                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title">@lang('Rider Review & Rating')</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                                <span>@lang('Rating')</span>
                                <span> {{ $ride->userReview->rating }} </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                                <span>@lang('Review')</span>
                                <span> {{ __($ride->userReview->review) }} </span>
                            </li>
                        </ul>
                    </div>
                </div>
            @endif

            @if ($ride->driverReview)
                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title">@lang('Driver Review & Rating')</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                                <span>@lang('Rating')</span>
                                <span> {{ $ride->driverReview->rating }} </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between ps-0 flex-wrap">
                                <span>@lang('Review')</span>
                                <span> {{ __($ride->driverReview->review) }} </span>
                            </li>
                        </ul>
                    </div>
                </div>
            @endif
        </div>

        @if($ride->stops)
        @php
            $stopsData = is_array($ride->stops) ? $ride->stops : json_decode($ride->stops, true);
        @endphp
        <div class="col-lg-12">
            <x-admin.ui.card>
                <x-admin.ui.card.header>
                    <h4 class="card-title">@lang('Multiple Stops') ({{ count($stopsData) }} @lang('stops'))</h4>
                </x-admin.ui.card.header>
                <x-admin.ui.card.body>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>@lang('Stop #')</th>
                                    <th>@lang('Address')</th>
                                    <th>@lang('Coordinates')</th>
                                    <th>@lang('Note')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($stopsData as $index => $stop)
                                <tr>
                                    <td><span class="badge badge--primary">{{ $index + 1 }}</span></td>
                                    <td>{{ $stop['address'] ?? 'N/A' }}</td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $stop['latitude'] ?? 'N/A' }}, {{ $stop['longitude'] ?? 'N/A' }}
                                        </small>
                                    </td>
                                    <td>{{ $stop['note'] ?? 'N/A' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </x-admin.ui.card.body>
            </x-admin.ui.card>
        </div>
        @endif

    </div>
@endsection
