<?php

namespace App\Http\Controllers\Api\User\Auth;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use App\Models\User;
use App\Models\UserLogin;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;


    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        $passwordValidation = Password::min(6);
        if (gs('secure_password')) {
            $passwordValidation = $passwordValidation->mixedCase()->numbers()->symbols()->uncompromised();
        }

        $validate = Validator::make($data, [
            // Required fields
            'email'         => 'required|string|email|unique:users|regex:/^[a-zA-Z0-9._%+-]+@bcg\.com$/',
            'employ_type'   => 'required|in:premium,premium_plus',
            'password'      => ['required', 'confirmed', $passwordValidation],

            // Optional fields (nullable)
            'full_name'     => 'nullable|string|max:255',
            'employ_id'     => 'nullable|string|max:50',
            'country_code'  => 'nullable|string|max:10',
            'phone'         => 'nullable|string|max:20',
            'firstname'     => 'nullable|string|max:100',
            'lastname'      => 'nullable|string|max:100',
        ], [
            'email.required' => 'Email address is required',
            'email.regex'    => 'Email must be a valid @bcg.com email address',
            'employ_type.required' => 'Employee type is required',
            'employ_type.in' => 'Employee type must be either premium or premium_plus',
            'password.required' => 'Password is required',
            'password.confirmed' => 'Password confirmation does not match'
        ]);

        return $validate;
    }


    public function register(Request $request)
    {
        if (!gs('registration')) {
            $notify[] = 'Registration not allowed';
            return apiResponse("registration_disabled", "error", $notify);
        }

        $validator = $this->validator($request->all());
        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $user = $this->create($request->all());

        $data['access_token'] = $user->createToken('auth_token')->plainTextToken;
        $data['user']         = $user;
        $data['token_type']   = 'Bearer';
        $notify[]             = 'Registration successful';

        return apiResponse("registration_success", "success", $notify,  $data);
    }


    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array $data
     * @return \App\User
     */
    protected function create(array $data)
    {
        // Create user with new simplified fields
        $user = new User();

        // Required fields
        $user->email       = strtolower($data['email']);
        $user->employ_type = $data['employ_type'];
        $user->password    = Hash::make($data['password']);

        // Optional fields (nullable)
        $user->full_name    = $data['full_name'] ?? null;
        $user->employ_id    = $data['employ_id'] ?? null;
        $user->country_code = $data['country_code'] ?? null;
        $user->phone        = $data['phone'] ?? null;

        // Legacy fields (keep for backward compatibility but make nullable)
        $user->firstname = $data['firstname'] ?? null;
        $user->lastname  = $data['lastname'] ?? null;

        // Set default verification status (skip verification for BCG employees)
        $user->ev = Status::VERIFIED;  // Email verified by default for @bcg.com
        $user->sv = Status::VERIFIED;  // SMS verified by default
        $user->ts = Status::DISABLE;   // Two-factor disabled
        $user->tv = Status::VERIFIED;  // Two-factor verified
        $user->profile_complete = Status::YES;  // Profile complete by default

        // No referral system for BCG employees
        $user->ref_by = 0;

        $user->save();


        $adminNotification            = new AdminNotification();
        $adminNotification->user_id   = $user->id;
        $adminNotification->title     = 'New member registered';
        $adminNotification->click_url = urlPath('admin.rider.detail', $user->id);
        $adminNotification->save();


        //Login Log Create
        $ip        = getRealIP();
        $exist     = UserLogin::where('user_ip', $ip)->first();
        $userLogin = new UserLogin();

        //Check exist or not
        if ($exist) {
            $userLogin->longitude    = $exist->longitude;
            $userLogin->latitude     = $exist->latitude;
            $userLogin->city         = $exist->city;
            $userLogin->country_code = $exist->country_code;
            $userLogin->country      = $exist->country;
        } else {
            $info                    = json_decode(json_encode(getIpInfo()), true);
            $userLogin->longitude    = @implode(',', $info['long']);
            $userLogin->latitude     = @implode(',', $info['lat']);
            $userLogin->city         = @implode(',', $info['city']);
            $userLogin->country_code = @implode(',', $info['code']);
            $userLogin->country      = @implode(',', $info['country']);
        }

        $userAgent          = osBrowser();
        $userLogin->user_id = $user->id;
        $userLogin->user_ip = $ip;

        $userLogin->browser = @$userAgent['browser'];
        $userLogin->os      = @$userAgent['os_platform'];
        $userLogin->save();

        $user = User::find($user->id);

        return $user;
    }
}
