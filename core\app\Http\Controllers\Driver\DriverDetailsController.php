<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DriverDetailsController extends Controller
{
    /**
     * Show driver details
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function show(Request $request): JsonResponse
    {
        try {
            $driver = auth()->guard('driver')->user();
            
            if (!$driver) {
                return response()->json([
                    'success' => false,
                    'message' => 'Driver not found',
                    'data' => null
                ], 404);
            }

            // Load relationships
            $driver->load([
                'service',
                'zone', 
                'vehicle',
                'vehicle.model',
                'vehicle.color',
                'vehicle.year',
                'reviews' => function($query) {
                    $query->latest()->limit(10);
                }
            ]);

            // Calculate additional statistics
            $totalRides = $driver->ride()->count();
            $completedRides = $driver->ride()->completed()->count();
            $totalEarnings = $driver->ride()->completed()->sum('amount');
            $averageRating = $driver->reviews()->avg('rating') ?? 0;
            $totalReviews = $driver->reviews()->count();

            // Prepare driver data
            $driverData = [
                'id' => $driver->id,
                'personal_info' => [
                    'firstname' => $driver->firstname,
                    'lastname' => $driver->lastname,
                    'fullname' => $driver->fullname,
                    'username' => $driver->username,
                    'email' => $driver->email,
                    'mobile' => $driver->mobile,
                    'dial_code' => $driver->dial_code,
                    'mobile_number' => $driver->mobileNumber,
                    'image' => $driver->imageSrc,
                    'address' => $driver->address,
                    'city' => $driver->city,
                    'state' => $driver->state,
                    'zip' => $driver->zip,
                    'country_name' => $driver->country_name,
                ],
                'professional_info' => [
                    'balance' => number_format($driver->balance, 2),
                    'online_status' => $driver->online_status,
                    'status' => $driver->status,
                    'profile_complete' => $driver->profile_complete,
                    'total_reviews' => $driver->total_reviews,
                    'avg_rating' => number_format($driver->avg_rating, 2),
                ],
                'verification_status' => [
                    'email_verified' => $driver->ev,
                    'mobile_verified' => $driver->sv,
                    'document_verified' => $driver->dv,
                    'vehicle_verified' => $driver->vv,
                    'two_factor_enabled' => $driver->tv,
                ],
                'service' => $driver->service ? [
                    'id' => $driver->service->id,
                    'name' => $driver->service->name,
                    'image' => $driver->service->image,
                ] : null,
                'zone' => $driver->zone ? [
                    'id' => $driver->zone->id,
                    'name' => $driver->zone->name,
                    'status' => $driver->zone->status,
                ] : null,
                'vehicle' => $driver->vehicle ? [
                    'id' => $driver->vehicle->id,
                    'registration_number' => $driver->vehicle->registration_number,
                    'model' => $driver->vehicle->model ? [
                        'id' => $driver->vehicle->model->id,
                        'name' => $driver->vehicle->model->name,
                    ] : null,
                    'color' => $driver->vehicle->color ? [
                        'id' => $driver->vehicle->color->id,
                        'name' => $driver->vehicle->color->name,
                    ] : null,
                    'year' => $driver->vehicle->year ? [
                        'id' => $driver->vehicle->year->id,
                        'year' => $driver->vehicle->year->year,
                    ] : null,
                    'image' => $driver->vehicle->image,
                ] : null,
                'statistics' => [
                    'total_rides' => $totalRides,
                    'completed_rides' => $completedRides,
                    'total_earnings' => number_format($totalEarnings, 2),
                    'average_rating' => number_format($averageRating, 2),
                    'total_reviews' => $totalReviews,
                    'completion_rate' => $totalRides > 0 ? number_format(($completedRides / $totalRides) * 100, 2) : 0,
                ],
                'recent_reviews' => $driver->reviews->map(function($review) {
                    return [
                        'id' => $review->id,
                        'rating' => $review->rating,
                        'comment' => $review->comment,
                        'created_at' => $review->created_at->format('Y-m-d H:i:s'),
                        'rider' => [
                            'name' => $review->user ? $review->user->fullname : 'Anonymous',
                        ]
                    ];
                }),
                'rider_rules' => $driver->riderRules()->pluck('name')->toArray(),
                'created_at' => $driver->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $driver->updated_at->format('Y-m-d H:i:s'),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Driver details retrieved successfully',
                'data' => $driverData
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve driver details',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
