<?php

namespace App\Models;

use App\Traits\GlobalStatus;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use  GlobalStatus;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'image',
        'city_min_fare',
        'city_max_fare',
        'city_recommend_fare',
        'city_fare_commission',
        'intercity_min_fare',
        'intercity_max_fare',
        'intercity_recommend_fare',
        'intercity_fare_commission',
        'half_hour_fare',
        'full_day_fare',
        'cancellation_charges',
        'no_of_passengers',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'city_min_fare' => 'decimal:8',
        'city_max_fare' => 'decimal:8',
        'city_recommend_fare' => 'decimal:8',
        'city_fare_commission' => 'decimal:2',
        'intercity_min_fare' => 'decimal:8',
        'intercity_max_fare' => 'decimal:8',
        'intercity_recommend_fare' => 'decimal:8',
        'intercity_fare_commission' => 'decimal:2',
        'half_hour_fare' => 'decimal:8',
        'full_day_fare' => 'decimal:8',
        'cancellation_charges' => 'decimal:8',
        'no_of_passengers' => 'integer',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        // Hidden fields - set to null to avoid calculation conflicts
        'city_min_fare' => null,
        'city_max_fare' => null,
        'city_recommend_fare' => null,
        'intercity_min_fare' => null,
        'intercity_max_fare' => null,
        'intercity_recommend_fare' => null,
        'intercity_fare_commission' => null,
    ];
}
