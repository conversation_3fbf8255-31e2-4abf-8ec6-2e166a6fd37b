<?php

namespace App\Http\Controllers\Admin;

use App\Models\Service;
use Illuminate\Http\Request;
use App\Rules\FileTypeValidate;
use App\Http\Controllers\Controller;

class ServiceController extends Controller
{
    public function index()
    {
        $pageTitle = 'All Services';
        $services  = Service::searchable(['name'])->orderBy('id', getOrderBy())->paginate(getPaginate());
        return view('admin.services.index', compact('pageTitle', 'services'));
    }

    public function store(Request $request, $id = 0)
    {
        $request->validate([
            'image'                => ['image', new FileTypeValidate(['jpg', 'jpeg', 'png'])],
            'name'                 => 'required|unique:services,name,' . $id,
            'half_hour_fare'       => 'required|numeric|gt:0',
            'full_day_fare'        => 'required|numeric|gt:0',
            'cancellation_charges' => 'required|numeric|gte:0',
            'no_of_passengers'     => 'required|integer|gt:0',
            'city_fare_commission' => 'required|numeric|gt:0|lt:100',
        ]);

        if ($id) {
            $service      = Service::findOrFail($id);
            $notification = 'Service updated successfully';
        } else {
            $service      = new Service();
            $notification = 'Service added successfully';
        }

        if ($request->hasFile('image')) {
            try {
                $service->image = fileUploader($request->image, getFilePath('service'), getFileSize('service'), @$service->image);
            } catch (\Exception $exp) {
                $notify[] = ['error', 'Couldn\'t upload your image'];
                return back()->withNotify($notify);
            }
        }

        $service->name                 = $request->name;
        $service->half_hour_fare       = $request->half_hour_fare;
        $service->full_day_fare        = $request->full_day_fare;
        $service->cancellation_charges = $request->cancellation_charges;
        $service->no_of_passengers     = $request->no_of_passengers;
        $service->city_fare_commission = $request->city_fare_commission;

        // Hidden fields - set to null to avoid calculation conflicts
        $service->city_min_fare             = null;
        $service->city_max_fare             = null;
        $service->city_recommend_fare       = null;
        $service->intercity_min_fare        = null;
        $service->intercity_max_fare        = null;
        $service->intercity_recommend_fare  = null;
        $service->intercity_fare_commission = null;

        $service->save();

        $notify[] = ['success',  $notification];
        return back()->withNotify($notify);
    }

    public function status($id)
    {
        return Service::changeStatus($id);
    }
}
