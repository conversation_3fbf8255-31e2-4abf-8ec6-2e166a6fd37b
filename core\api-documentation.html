<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BCG Corporate Ride API Documentation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1e40af;
            --primary-light: #dbeafe;
            --success: #10b981;
            --success-light: #d1fae5;
            --warning: #f59e0b;
            --warning-light: #fef3c7;
            --error: #ef4444;
            --error-light: #fee2e2;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: var(--gray-50);
        }

        .container {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        /* Header Styles */
        header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            letter-spacing: -0.025em;
        }

        header p {
            font-size: 1.125rem;
            opacity: 0.9;
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: white;
            padding: 2rem 1rem;
            border-right: 1px solid var(--gray-200);
            position: fixed;
            width: 280px;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar nav ul {
            list-style: none;
        }

        .sidebar nav ul li {
            margin-bottom: 0.5rem;
        }

        .sidebar nav ul li a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--gray-600);
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .sidebar nav ul li a:hover {
            background-color: var(--primary-light);
            color: var(--primary);
        }

        /* Main Content Styles */
        .main-content {
            padding: 2rem;
            margin-left: 280px;
        }

        section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        section h2 {
            color: var(--primary);
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--gray-200);
        }

        /* Endpoint Styles */
        .endpoint {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.2s ease;
        }

        .endpoint:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .endpoint h3 {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: var(--gray-800);
            font-size: 1.25rem;
            font-weight: 600;
        }

        .method {
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            font-family: 'Fira Code', monospace;
        }

        .get { background-color: var(--primary-light); color: var(--primary); }
        .post { background-color: var(--success-light); color: var(--success); }
        .put { background-color: var(--warning-light); color: var(--warning); }
        .delete { background-color: var(--error-light); color: var(--error); }

        .url {
            font-family: 'Fira Code', monospace;
            background-color: var(--gray-800);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            display: inline-block;
            margin: 0.5rem 0;
            font-size: 0.875rem;
        }

        /* Parameter Styles */
        .params {
            background-color: var(--gray-50);
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }

        .params ul {
            list-style: none;
        }

        .param {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background-color: white;
        }

        .param-name {
            font-family: 'Fira Code', monospace;
            color: var(--primary);
            font-weight: 600;
        }

        .required {
            color: var(--error);
            font-weight: 600;
        }

        /* Response Styles */
        .response {
            margin-top: 1.5rem;
        }

        .response h4 {
            margin-bottom: 0.75rem;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-700);
        }

        pre {
            background-color: var(--gray-800);
            color: var(--gray-200);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            font-family: 'Fira Code', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        /* Badge Styles */
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .new {
            background-color: var(--warning-light);
            color: var(--warning);
        }

        .deprecated {
            background-color: var(--error-light);
            color: var(--error);
        }

        /* Code Highlighting */
        .json-key { color: #9cdcfe; }
        .json-string { color: #ce9178; }
        .json-number { color: #b5cea8; }
        .json-boolean { color: #569cd6; }
        .json-null { color: #569cd6; }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--gray-100);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: relative;
                width: 100%;
                height: auto;
            }

            .main-content {
                margin-left: 0;
            }
        }

        /* Additional Features */
        .note {
            background-color: var(--primary-light);
            border-left: 4px solid var(--primary);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
        }

        .note p {
            color: var(--primary-dark);
            font-size: 0.875rem;
        }

        .example {
            background-color: var(--gray-50);
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }

        .example h4 {
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .example pre {
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>BCG Corporate Ride API</h1>
            <p>Comprehensive API documentation for BCG Corporate Ride System</p>
        </header>

        <div class="sidebar">
            <nav>
                <ul>
                    <li><a href="#introduction">Introduction</a></li>
                    <li><a href="#authentication">Authentication</a></li>
                    <li><a href="#profile">Profile Management</a></li>
                    <li><a href="#rides">Ride Management</a></li>
                    <li><a href="#payments">Payment Management</a></li>
                    <li><a href="#notifications">Notifications</a></li>
                </ul>
            </nav>
        </div>

        <div class="main-content">
            <section id="introduction">
                <h2>Introduction</h2>
                <p>Welcome to the BCG Corporate Ride API documentation. This API provides endpoints for managing corporate rides, driver assignments, and payments.</p>
                
                <div class="note">
                    <p>All API requests must include the required headers and authentication tokens.</p>
                </div>

                <h3>Key Features</h3>
                <ul>
                    <li>Email-only registration</li>
                    <li>Direct driver selection</li>
                    <li>30-minute cycle billing</li>
                    <li>Multiple stops support</li>
                    <li>Corporate payment options</li>
                </ul>

                <h3>Required Headers</h3>
                <div class="example">
                    <pre>
Content-Type: application/json
Accept: application/json
dev-token: YOUR_DEV_TOKEN
Authorization: Bearer YOUR_ACCESS_TOKEN</pre>
                </div>
            </section>

            <section id="authentication">
                <h2>Authentication APIs</h2>

                <div class="endpoint">
                    <h3>
                        <span class="method post">POST</span>
                        User Registration
                    </h3>
                    <p class="url">/user/register</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param">
                                <span class="param-name">firstname</span>
                                <span>User's first name</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">lastname</span>
                                <span>User's last name</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">email</span>
                                <span>Valid email address</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">password</span>
                                <span>Secure password</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">password_confirmation</span>
                                <span>Password confirmation</span>
                                <span class="required">*</span>
                            </li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Registration successful",
    "data": {
        "user": {
            "id": 1,
            "firstname": "John",
            "lastname": "Doe",
            "email": "<EMAIL>"
        },
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "Bearer"
    }
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>
                        <span class="method post">POST</span>
                        User Login
                    </h3>
                    <p class="url">/user/login</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param">
                                <span class="param-name">email</span>
                                <span>Registered email address</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">password</span>
                                <span>Account password</span>
                                <span class="required">*</span>
                            </li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "firstname": "John",
            "lastname": "Doe",
            "email": "<EMAIL>"
        },
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "Bearer"
    }
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>
                        <span class="method post">POST</span>
                        Forgot Password
                    </h3>
                    <p class="url">/user/forgot-password</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param">
                                <span class="param-name">email</span>
                                <span>Registered email address</span>
                                <span class="required">*</span>
                            </li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Verification code sent to your email",
    "data": null
}</pre>
                        <h4>❌ Error Response</h4>
                        <pre>
{
    "success": false,
    "message": "Email does not exist",
    "data": null
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>
                        <span class="method post">POST</span>
                        Reset Password
                    </h3>
                    <p class="url">/user/reset-password</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param">
                                <span class="param-name">email</span>
                                <span>Registered email address</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">code</span>
                                <span>Verification code received via email</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">password</span>
                                <span>New password (min: 6 characters)</span>
                                <span class="required">*</span>
                            </li>
                            <li class="param">
                                <span class="param-name">password_confirmation</span>
                                <span>Confirm new password</span>
                                <span class="required">*</span>
                            </li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Password changed successfully",
    "data": null
}</pre>
                        <h4>❌ Error Responses</h4>
                        <pre>
{
    "success": false,
    "message": "Verification code does not match",
    "data": null
}</pre>
                        <pre>
{
    "success": false,
    "message": "Verification code expired",
    "data": null
}</pre>
                    </div>
                </div>
            </section>

            <section id="profile">
                <h2>Profile Management APIs</h2>

                <div class="endpoint">
                    <h3>Get User Profile</h3>
                    <span class="method get">GET</span>
                    <p class="url">/user/profile</p>
                    <div class="response">
                        <p>Returns user profile information including:</p>
                        <ul>
                            <li>Personal details</li>
                            <li>BCG employee information</li>
                            <li>Profile completion status</li>
                        </ul>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>Update BCG Employee Profile</h3>
                    <span class="method post">POST</span>
                    <p class="url">/user/update-profile</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param"><span class="param-name">firstname</span> - First name <span class="required">*</span></li>
                            <li class="param"><span class="param-name">lastname</span> - Last name <span class="required">*</span></li>
                            <li class="param"><span class="param-name">email</span> - Email address <span class="required">*</span></li>
                            <li class="param"><span class="param-name">mobile</span> - Mobile number <span class="required">*</span></li>
                            <li class="param"><span class="param-name">address</span> - Address <span class="required">*</span></li>
                            <li class="param"><span class="param-name">city</span> - City <span class="required">*</span></li>
                            <li class="param"><span class="param-name">state</span> - State <span class="required">*</span></li>
                            <li class="param"><span class="param-name">zip</span> - ZIP code <span class="required">*</span></li>
                            <li class="param"><span class="param-name">country</span> - Country <span class="required">*</span></li>
                        </ul>
                    </div>
                </div>
            </section>

            <section id="rides">
                <h2>Ride Management APIs</h2>

                <div class="endpoint">
                    <h3>Get Available Drivers List</h3>
                    <span class="method get">GET</span>
                    <p class="url">/ride/available-drivers-list</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param"><span class="param-name">service_id</span> - Service type ID <span class="required">*</span></li>
                            <li class="param"><span class="param-name">latitude</span> - Current latitude <span class="required">*</span></li>
                            <li class="param"><span class="param-name">longitude</span> - Current longitude <span class="required">*</span></li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Available drivers retrieved successfully",
    "data": {
        "drivers": [
            {
                "id": 1,
                "firstname": "John",
                "lastname": "Doe",
                "email": "<EMAIL>",
                "mobile": "1234567890",
                "rating": 4.5,
                "vehicle": {
                    "id": 1,
                    "model": "Toyota Camry",
                    "color": "Black",
                    "year": "2023"
                }
            }
        ],
        "service": {
            "id": 1,
            "name": "Sedan",
            "description": "Standard 4-passenger vehicle"
        },
        "zone": {
            "id": 1,
            "name": "Downtown"
        }
    }
}
                        </pre>
                        <h4>❌ Error Responses</h4>
                        <pre>
{
    "success": false,
    "message": "No drivers available in your area for the selected service",
    "data": null
}
                        </pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>Check Service Availability</h3>
                    <span class="method post">POST</span>
                    <p class="url">/ride/available-drivers</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param"><span class="param-name">service_id</span> - Service type ID <span class="required">*</span></li>
                            <li class="param"><span class="param-name">pickup_latitude</span> - Pickup latitude <span class="required">*</span></li>
                            <li class="param"><span class="param-name">pickup_longitude</span> - Pickup longitude <span class="required">*</span></li>
                            <li class="param"><span class="param-name">ride_option</span> - Ride option (go_now/schedule) <span class="required">*</span></li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Driver will be automatically assigned by system",
    "data": {
        "service_available": true,
        "fare_data": {
            "estimated_fare": 25.00,
            "currency": "USD",
            "estimated_duration": 30
        },
        "ride_option": "go_now",
        "pickup_zone": {
            "id": 1,
            "name": "Downtown"
        },
        "service_types": [
            {
                "id": 1,
                "name": "Sedan",
                "description": "Standard 4-passenger vehicle"
            }
        ]
    }
}
                        </pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>Create Ride</h3>
                    <span class="method post">POST</span>
                    <p class="url">/ride/create</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param"><span class="param-name">service_id</span> - Service type ID <span class="required">*</span></li>
                            <li class="param"><span class="param-name">pickup_latitude</span> - Pickup latitude <span class="required">*</span></li>
                            <li class="param"><span class="param-name">pickup_longitude</span> - Pickup longitude <span class="required">*</span></li>
                            <li class="param"><span class="param-name">destination_latitude</span> - Destination latitude</li>
                            <li class="param"><span class="param-name">destination_longitude</span> - Destination longitude</li>
                            <li class="param"><span class="param-name">ride_option</span> - Ride option (go_now/schedule) <span class="required">*</span></li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Ride created successfully",
    "data": {
        "ride": {
            "id": 1,
            "status": "pending",
            "pickup_address": "123 Main St",
            "destination_address": "456 Park Ave",
            "estimated_fare": 25.00,
            "driver": {
                "id": 1,
                "name": "John Doe",
                "vehicle": "Toyota Camry"
            }
        }
    }
}
                        </pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>Get Ride History</h3>
                    <span class="method get">GET</span>
                    <p class="url">/ride/history</p>
                    <div class="response">
                        <p>Returns paginated list of ride history with:</p>
                        <ul>
                            <li>Ride details</li>
                            <li>Driver information</li>
                            <li>Payment status</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section id="payments">
                <h2>Payment Management APIs</h2>

                <div class="endpoint">
                    <h3>Get Payment Methods</h3>
                    <span class="method get">GET</span>
                    <p class="url">/payment/methods</p>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Payment methods retrieved successfully",
    "data": {
        "methods": [
            {
                "id": 1,
                "name": "Credit Card",
                "min_amount": 10.00,
                "max_amount": 1000.00,
                "charge": 2.5
            }
        ]
    }
}
                        </pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>Get Transaction History</h3>
                    <span class="method get">GET</span>
                    <p class="url">/payment/transactions</p>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Transaction history retrieved successfully",
    "data": {
        "transactions": [
            {
                "id": 1,
                "amount": 25.00,
                "type": "ride_payment",
                "status": "completed",
                "created_at": "2024-03-28T22:00:00.000000Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 1
        }
    }
}
                        </pre>
                    </div>
                </div>
            </section>

            <section id="notifications">
                <h2>Notification APIs</h2>

                <div class="endpoint">
                    <h3>Save Device Token</h3>
                    <span class="method post">POST</span>
                    <p class="url">/user/save-device-token</p>
                    <div class="params">
                        <p>Required Parameters:</p>
                        <ul>
                            <li class="param"><span class="param-name">token</span> - Device token for push notifications <span class="required">*</span></li>
                        </ul>
                    </div>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Device token saved successfully",
    "data": null
}
                        </pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h3>Get Push Notifications</h3>
                    <span class="method get">GET</span>
                    <p class="url">/user/push-notifications</p>
                    <div class="response">
                        <h4>✅ Success Response</h4>
                        <pre>
{
    "success": true,
    "message": "Notifications retrieved successfully",
    "data": {
        "notifications": [
            {
                "id": 1,
                "title": "Ride Update",
                "message": "Your ride has been confirmed",
                "created_at": "2024-03-28T22:00:00.000000Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 1
        }
    }
}
                        </pre>
                    </div>
                </div>
            </section>
        </div>
    </div>
</body>
</html>