<?php

namespace Tests\Feature;

use App\Models\Driver;
use App\Models\Service;
use App\Models\Zone;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use App\Models\VehicleColor;
use App\Models\VehicleYear;
use App\Models\Brand;
use App\Models\Review;
use App\Models\Ride;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class DriverDetailsApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create necessary test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create a service
        $this->service = Service::create([
            'name' => 'Car Service',
            'image' => 'car.png',
            'status' => 1
        ]);

        // Create a zone
        $this->zone = Zone::create([
            'name' => 'Test Zone',
            'coordinates' => json_encode([
                ['lat' => 40.7128, 'lng' => -74.0060],
                ['lat' => 40.7589, 'lng' => -73.9851],
                ['lat' => 40.7505, 'lng' => -73.9934]
            ]),
            'status' => 1
        ]);

        // Create a brand
        $this->brand = Brand::create([
            'name' => 'Toyota',
            'image' => 'toyota.png',
            'status' => 1
        ]);

        // Create vehicle model
        $this->model = VehicleModel::create([
            'name' => 'Camry',
            'brand_id' => $this->brand->id,
            'status' => 1
        ]);

        // Create vehicle color
        $this->color = VehicleColor::create([
            'name' => 'White',
            'status' => 1
        ]);

        // Create vehicle year
        $this->year = VehicleYear::create([
            'name' => '2022',
            'status' => 1
        ]);

        // Create a driver
        $this->driver = Driver::create([
            'firstname' => 'John',
            'lastname' => 'Doe',
            'username' => 'john_driver',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'mobile' => '1234567890',
            'dial_code' => '+1',
            'address' => '123 Main St',
            'city' => 'New York',
            'state' => 'NY',
            'zip' => '10001',
            'country_name' => 'United States',
            'service_id' => $this->service->id,
            'zone_id' => $this->zone->id,
            'balance' => 1250.00,
            'online_status' => 1,
            'status' => 1,
            'profile_complete' => 1,
            'ev' => 1,
            'sv' => 1,
            'dv' => 1,
            'vv' => 1,
            'total_reviews' => 45,
            'avg_rating' => 4.75
        ]);

        // Create a vehicle for the driver
        $this->vehicle = Vehicle::create([
            'driver_id' => $this->driver->id,
            'service_id' => $this->service->id,
            'brand_id' => $this->brand->id,
            'model_id' => $this->model->id,
            'color_id' => $this->color->id,
            'year_id' => $this->year->id,
            'vehicle_number' => 'ABC123',
            'image' => 'vehicle.jpg'
        ]);
    }

    public function test_driver_details_api_returns_success_response()
    {
        // Authenticate the driver
        Sanctum::actingAs($this->driver, ['driver_token']);

        // Make the API request
        $response = $this->getJson('/api/driver/driver-details');

        // Assert the response
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'driver' => [
                            'id',
                            'personal_info' => [
                                'firstname',
                                'lastname',
                                'fullname',
                                'username',
                                'email',
                                'mobile',
                                'dial_code',
                                'mobile_number',
                                'address',
                                'city',
                                'state',
                                'zip',
                                'country_name'
                            ],
                            'professional_info' => [
                                'balance',
                                'online_status',
                                'status',
                                'profile_complete',
                                'total_reviews',
                                'avg_rating'
                            ],
                            'verification_status' => [
                                'email_verified',
                                'mobile_verified',
                                'document_verified',
                                'vehicle_verified',
                                'two_factor_enabled'
                            ],
                            'service',
                            'zone',
                            'vehicle',
                            'statistics',
                            'recent_reviews',
                            'rider_rules',
                            'created_at',
                            'updated_at'
                        ],
                        'driver_image_path',
                        'vehicle_image_path',
                        'service_image_path',
                        'brand_image_path'
                    ]
                ]);

        // Assert specific data
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals('Driver details retrieved successfully', $responseData['message']);
        $this->assertEquals('John', $responseData['data']['driver']['personal_info']['firstname']);
        $this->assertEquals('Doe', $responseData['data']['driver']['personal_info']['lastname']);
        $this->assertEquals('<EMAIL>', $responseData['data']['driver']['personal_info']['email']);
    }

    public function test_driver_details_api_requires_authentication()
    {
        // Make the API request without authentication
        $response = $this->getJson('/api/driver/driver-details');

        // Assert unauthorized response
        $response->assertStatus(401);
    }

    public function test_driver_details_api_includes_vehicle_information()
    {
        // Authenticate the driver
        Sanctum::actingAs($this->driver, ['driver_token']);

        // Make the API request
        $response = $this->getJson('/api/driver/driver-details');

        // Assert vehicle information is included
        $response->assertStatus(200);
        $responseData = $response->json();
        
        $vehicle = $responseData['data']['driver']['vehicle'];
        $this->assertNotNull($vehicle);
        $this->assertEquals('ABC123', $vehicle['vehicle_number']);
        $this->assertEquals('Camry', $vehicle['model']['name']);
        $this->assertEquals('White', $vehicle['color']['name']);
        $this->assertEquals('2022', $vehicle['year']['name']);
        $this->assertEquals('Toyota', $vehicle['brand']['name']);
    }

    public function test_driver_details_api_includes_statistics()
    {
        // Create some rides for statistics
        Ride::create([
            'driver_id' => $this->driver->id,
            'user_id' => 1,
            'service_id' => $this->service->id,
            'pickup_location' => 'Test Location',
            'destination' => 'Test Destination',
            'amount' => 25.50,
            'status' => 3, // Completed status
        ]);

        // Authenticate the driver
        Sanctum::actingAs($this->driver, ['driver_token']);

        // Make the API request
        $response = $this->getJson('/api/driver/driver-details');

        // Assert statistics are included
        $response->assertStatus(200);
        $responseData = $response->json();
        
        $statistics = $responseData['data']['driver']['statistics'];
        $this->assertArrayHasKey('total_rides', $statistics);
        $this->assertArrayHasKey('completed_rides', $statistics);
        $this->assertArrayHasKey('total_earnings', $statistics);
        $this->assertArrayHasKey('average_rating', $statistics);
        $this->assertArrayHasKey('completion_rate', $statistics);
    }
}
