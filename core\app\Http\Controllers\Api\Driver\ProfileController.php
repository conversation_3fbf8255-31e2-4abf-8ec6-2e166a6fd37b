<?php

namespace App\Http\Controllers\Api\Driver;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProfileController extends Controller
{
    /**
     * Display the authenticated driver's profile details.
     */
    public function show(Request $request)
    {
        $driver = Auth::guard('driver')->user();
        if (!$driver) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        return response()->json(['data' => $driver]);
    }
}
