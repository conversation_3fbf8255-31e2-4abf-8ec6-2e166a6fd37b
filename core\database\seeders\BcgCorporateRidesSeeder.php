<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Ride;
use App\Models\User;
use App\Models\Driver;
use App\Models\Service;
use App\Models\Zone;
use App\Constants\Status;
use Carbon\Carbon;

class BcgCorporateRidesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get BCG users (employees with @bcg.com email)
        $bcgUsers = User::where('email', 'like', '%@bcg.com')->get();

        // If no BCG users exist, create some
        if ($bcgUsers->isEmpty()) {
            $bcgUsers = collect([
                User::create([
                    'firstname' => 'John',
                    'lastname' => 'Doe',
                    'email' => '<EMAIL>',
                    'full_name' => '<PERSON>',
                    'employ_type' => 'premium',
                    'employ_id' => 'BCG001',
                    'phone' => '******-0123',
                    'country_code' => '+1',
                    'password' => bcrypt('password123'),
                    'ev' => 1,
                    'sv' => 1,
                    'profile_complete' => 1,
                ]),
                User::create([
                    'firstname' => 'Jane',
                    'lastname' => 'Smith',
                    'email' => '<EMAIL>',
                    'full_name' => 'Jane Smith',
                    'employ_type' => 'premium_plus',
                    'employ_id' => 'BCG002',
                    'phone' => '******-0124',
                    'country_code' => '+1',
                    'password' => bcrypt('password123'),
                    'ev' => 1,
                    'sv' => 1,
                    'profile_complete' => 1,
                ]),
                User::create([
                    'firstname' => 'Michael',
                    'lastname' => 'Johnson',
                    'email' => '<EMAIL>',
                    'full_name' => 'Michael Johnson',
                    'employ_type' => 'premium',
                    'employ_id' => 'BCG003',
                    'phone' => '******-0125',
                    'country_code' => '+1',
                    'password' => bcrypt('password123'),
                    'ev' => 1,
                    'sv' => 1,
                    'profile_complete' => 1,
                ]),
            ]);
        }

        // Get available drivers and services (or use defaults)
        $drivers = Driver::where('dv', Status::VERIFIED)->where('vv', Status::VERIFIED)->get();
        $services = Service::where('status', Status::ENABLE)->get();
        $zones = Zone::where('status', Status::ENABLE)->get();

        // Use first available or create default IDs
        $driverId = $drivers->first()->id ?? 1;
        $serviceId = $services->first()->id ?? 1;
        $zoneId = $zones->first()->id ?? 1;

        // BCG Case Codes
        $caseCodes = [
            'BCG-2025-001234', 'BCG-2025-001235', 'BCG-2025-001236', 'BCG-2025-001237',
            'BCG-2025-001238', 'BCG-2025-001239', 'BCG-2025-001240', 'BCG-2025-001241',
            'BCG-2025-001242', 'BCG-2025-001243', 'BCG-2025-001244', 'BCG-2025-001245',
            'BCG-2025-001246', 'BCG-2025-001247', 'BCG-2025-001248', 'BCG-2025-001249',
            'BCG-2025-001250', 'BCG-2025-001251', 'BCG-2025-001252', 'BCG-2025-001253'
        ];

        // Pickup and destination locations
        $locations = [
            ['name' => 'BCG Boston Office', 'lat' => 42.3601, 'lng' => -71.0589],
            ['name' => 'Logan International Airport', 'lat' => 42.3656, 'lng' => -71.0096],
            ['name' => 'Boston Convention Center', 'lat' => 42.3456, 'lng' => -71.0447],
            ['name' => 'Harvard Business School', 'lat' => 42.3656, 'lng' => -71.1244],
            ['name' => 'MIT Campus', 'lat' => 42.3601, 'lng' => -71.0942],
            ['name' => 'Boston Harbor Hotel', 'lat' => 42.3584, 'lng' => -71.0520],
            ['name' => 'Prudential Center', 'lat' => 42.3467, 'lng' => -71.0818],
            ['name' => 'Boston Common', 'lat' => 42.3550, 'lng' => -71.0662],
        ];

        // Multiple stops examples
        $multipleStops = [
            [
                ['latitude' => 42.3505, 'longitude' => -71.0934, 'address' => 'Client Office A', 'note' => '30-minute meeting'],
                ['latitude' => 42.3614, 'longitude' => -71.0776, 'address' => 'Document Pickup', 'note' => 'Collect contracts'],
            ],
            [
                ['latitude' => 42.3656, 'longitude' => -71.1244, 'address' => 'Harvard Business School', 'note' => 'Guest lecture'],
                ['latitude' => 42.3601, 'longitude' => -71.0942, 'address' => 'MIT Campus', 'note' => 'Research meeting'],
                ['latitude' => 42.3584, 'longitude' => -71.0520, 'address' => 'Harbor Hotel', 'note' => 'Client dinner'],
            ],
            [
                ['latitude' => 42.3467, 'longitude' => -71.0818, 'address' => 'Prudential Center', 'note' => 'Team pickup'],
            ],
        ];

        // Create 20 BCG Corporate rides
        for ($i = 0; $i < 20; $i++) {
            $user = $bcgUsers->random();

            $pickupLocation = $locations[array_rand($locations)];
            $destinationLocation = $locations[array_rand($locations)];

            // Ensure pickup and destination are different
            while ($pickupLocation['name'] === $destinationLocation['name']) {
                $destinationLocation = $locations[array_rand($locations)];
            }

            // Random ride option
            $rideOption = rand(0, 1) ? 'go_now' : 'schedule';

            // Random estimated duration (30 minutes to 8 hours)
            $estimatedDuration = rand(30, 480);
            $cycles = max(1, ceil($estimatedDuration / 30));
            $farePerCycle = rand(20, 35); // $20-$35 per cycle
            $totalFare = $cycles * $farePerCycle;

            // Random number of passengers (1-4)
            $passengers = rand(1, 4);

            // Random stops (30% chance of having stops)
            $stops = null;
            if (rand(1, 10) <= 3) {
                $stops = $multipleStops[array_rand($multipleStops)];
            }

            // Random status
            $statuses = [Status::RIDE_PENDING, Status::RIDE_ACTIVE, Status::RIDE_RUNNING, Status::RIDE_COMPLETED, Status::RIDE_END];
            $status = $statuses[array_rand($statuses)];

            // Scheduled time (if schedule option)
            $scheduledTime = null;
            if ($rideOption === 'schedule') {
                $scheduledTime = Carbon::now()->addHours(rand(3, 72)); // 3 to 72 hours from now
            }

            // Random notes for variety
            $notes = [
                'BCG Corporate ride - Client meeting',
                'BCG Corporate ride - Airport transfer',
                'BCG Corporate ride - Business conference',
                'BCG Corporate ride - Team building event',
                'BCG Corporate ride - Document pickup',
                'BCG Corporate ride - Site visit',
                'BCG Corporate ride - Executive transport',
                'BCG Corporate ride - Multi-stop business trip'
            ];
            $note = $notes[array_rand($notes)];

            Ride::create([
                'uid' => 'BCG' . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                'user_id' => $user->id,
                'driver_id' => $driverId,
                'service_id' => $serviceId,
                'pickup_location' => $pickupLocation['name'],
                'pickup_latitude' => $pickupLocation['lat'],
                'pickup_longitude' => $pickupLocation['lng'],
                'destination' => $destinationLocation['name'],
                'destination_latitude' => $destinationLocation['lat'],
                'destination_longitude' => $destinationLocation['lng'],
                'ride_type' => Status::CITY_RIDE,
                'note' => $note,
                'number_of_passenger' => $passengers,
                'distance' => round(rand(5, 50) / 10, 1), // 0.5 to 5.0 km
                'duration' => $estimatedDuration . ' minutes',
                'pickup_zone_id' => $zoneId,
                'destination_zone_id' => $zoneId,
                'recommend_amount' => $totalFare,
                'min_amount' => $farePerCycle,
                'max_amount' => 48 * $farePerCycle, // 24 hours max
                'amount' => $totalFare,
                'payment_type' => Status::PAYMENT_TYPE_CASH, // Company pays
                'commission_percentage' => rand(10, 20),
                'gateway_currency_id' => 0,
                'status' => $status,
                // BCG Corporate fields
                'ride_option' => $rideOption,
                'scheduled_time' => $scheduledTime,
                'estimated_duration' => $estimatedDuration,
                'case_code' => $caseCodes[$i],
                'stops' => $stops,
                'cycles_count' => $cycles,
                'fare_per_cycle' => $farePerCycle,
                'created_at' => Carbon::now()->subDays(rand(0, 30)),
                'updated_at' => Carbon::now()->subDays(rand(0, 30)),
            ]);
        }

        $this->command->info('Successfully created 20 BCG Corporate rides with realistic data!');
    }
}