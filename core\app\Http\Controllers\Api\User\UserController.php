<?php

namespace App\Http\Controllers\Api\User;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\DeviceToken;
use App\Models\GatewayCurrency;
use App\Models\NotificationLog;
use App\Models\Review;
use App\Models\Ride;
use App\Models\RidePayment;
use App\Models\Service;
use App\Models\Transaction;
use App\Rules\FileTypeValidate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\User;

class UserController extends Controller
{

    public function dashboard()
    {
        $notify[]    = 'User Dashboard';
        $services    = Service::active()->orderBy('name')->get();
        $user        = auth()->user();
        $runningRide = Ride::running()->where('user_id', $user->id)->with(['user', 'driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year'])->first();

        $paymentMethod = GatewayCurrency::whereHas('method', function ($gate) {
            $gate->active()->automatic();
        })->with('method')->orderby('method_code')->get();

        return apiResponse("dashboard", "success", $notify, [
            'user'               => $user,
            'payment_method'     => $paymentMethod,
            'services'           => $services,
            'running_ride'       => $runningRide,
            'service_image_path' => getFilePath('service'),
            'gateway_image_path' => getFilePath('gateway'),
            'user_image_path'    => getFilePath('user'),
        ]);
    }

    public function userDataSubmit(Request $request)
    {
        $user = auth()->user();

        if ($user->profile_complete == Status::YES) {
            $notify[] = 'You\'ve already completed your profile';
            return apiResponse("already_completed", "error", $notify);
        }

        $countryData  = (array)json_decode(file_get_contents(resource_path('views/partials/country.json')));
        $countryCodes = implode(',', array_keys($countryData));
        $mobileCodes  = implode(',', array_column($countryData, 'dial_code'));
        $countries    = implode(',', array_column($countryData, 'country'));


        $validator = Validator::make($request->all(), [
            'country_code' => 'required|in:' . $countryCodes,
            'country'      => 'required|in:' . $countries,
            'mobile_code'  => 'required|in:' . $mobileCodes,
            'username'     => 'required|unique:users|min:6',
            'mobile'       => ['required', 'regex:/^([0-9]*)$/', Rule::unique('users')->where('dial_code', $request->mobile_code)],
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }


        if (preg_match("/[^a-z0-9_]/", trim($request->username))) {
            $notify[] = 'No special character, space or capital letters in username';
            return apiResponse("validation_error", "error", $notify);
        }


        $user->country_code = $request->country_code;
        $user->mobile       = $request->mobile;
        $user->username     = $request->username;

        $user->address      = $request->address;
        $user->city         = $request->city;
        $user->state        = $request->state;
        $user->zip          = $request->zip;
        $user->country_name = @$request->country;
        $user->dial_code    = $request->mobile_code;

        $user->profile_complete = Status::YES;
        $user->save();

        $notify[] = 'Profile completed successfully';

        return apiResponse("profile_completed", "success", $notify, [
            'user' => $user
        ]);
    }

    public function paymentHistory()
    {
        $payments = RidePayment::where('rider_id', auth()->id())->orderBy('id', 'desc')->with('rider', 'ride', 'driver')->paginate(getPaginate());
        $notify[] = 'Payment Data';
        return apiResponse("payments", "success", $notify, [
            'payments' => $payments,
        ]);
    }

    public function transactions(Request $request)
    {
        $remarks      = Transaction::distinct('remark')->get('remark');
        $transactions = Transaction::where('user_id', auth()->id());

        if ($request->search) {
            $transactions = $transactions->where('trx', $request->search);
        }

        if ($request->type) {
            $type         = $request->type == 'plus' ? '+' : '-';
            $transactions = $transactions->where('trx_type', $type);
        }

        if ($request->remark) {
            $transactions = $transactions->where('remark', $request->remark);
        }

        $transactions = $transactions->orderBy('id', 'desc')->paginate(getPaginate());
        $notify[]     = 'Transactions data';

        return apiResponse("transactions", "success", $notify, [
            'transactions' => $transactions,
            'remarks'      => $remarks,
        ]);
    }

    public function submitProfile(Request $request)
    {
        $user = auth()->user();

        // Check if user is BCG employee or legacy user
        $isBcgEmployee = !empty($user->employ_type);

        if ($isBcgEmployee) {
            // BCG Employee profile update validation
            $validator = Validator::make($request->all(), [
                'full_name'     => 'nullable|string|max:255',
                'employ_id'     => 'nullable|string|max:50',
                'phone'         => 'nullable|string|max:20',
                'country_code'  => 'nullable|string|max:10',
                'image'         => ['nullable', 'image', new FileTypeValidate(['jpg', 'jpeg', 'png'])]
            ], [
                'full_name.required' => 'Full name is required',
            ]);
        } else {
            // Legacy user profile update validation
            $validator = Validator::make($request->all(), [
                'firstname' => 'required',
                'lastname'  => 'required',
                'image'     => ['nullable', 'image', new FileTypeValidate(['jpg', 'jpeg', 'png'])]
            ], [
                'firstname.required' => 'The first name field is required',
                'lastname.required'  => 'The last name field is required'
            ]);
        }

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        if ($isBcgEmployee) {
            // Update BCG employee fields
            $user->full_name    = $request->full_name;
            $user->employ_id    = $request->employ_id;
            $user->phone        = $request->phone;
            $user->country_code = $request->country_code;
        } else {
            // Update legacy user fields
            $user->firstname = $request->firstname;
            $user->lastname  = $request->lastname;
        }

        // Common fields for both user types
        $user->address = $request->address;
        $user->city    = $request->city;
        $user->state   = $request->state;
        $user->zip     = $request->zip;

        if ($request->hasFile('image')) {
            try {
                $user->image = fileUploader($request->image, getFilePath('user'), getFileSize('user'), $user->image);
            } catch (\Exception $exp) {
                $notify[] = 'Couldn\'t upload your image';
                return apiResponse('exception', 'error', $notify);
            }
        }

        $user->save();

        $notify[] = 'Profile updated successfully';

        // Return updated user data
        $userData = [
            'id'               => $user->id,
            'email'            => $user->email,
            'employ_type'      => $user->employ_type,
            'full_name'        => $user->full_name,
            'employ_id'        => $user->employ_id,
            'phone'            => $user->phone,
            'country_code'     => $user->country_code,
            'firstname'        => $user->firstname,
            'lastname'         => $user->lastname,
            'address'          => $user->address,
            'city'             => $user->city,
            'state'            => $user->state,
            'zip'              => $user->zip,
            'image_src'        => $user->image_src,
            'updated_at'       => $user->updated_at,
        ];

        return apiResponse("profile_updated", "success", $notify, [
            'user' => $userData
        ]);
    }

    /**
     * BCG Employee Profile Update - Dedicated endpoint for BCG employees
     */
    public function updateBcgProfile(Request $request)
    {
        $user = auth()->user();

        // Validate that user is BCG employee
        if (empty($user->employ_type)) {
            $notify[] = 'This endpoint is only for BCG employees';
            return apiResponse("not_bcg_employee", "error", $notify);
        }

        $validator = Validator::make($request->all(), [
            'full_name'     => 'required|string|max:255',
            'employ_id'     => 'nullable|string|max:50',
            'phone'         => 'nullable|string|max:20',
            'country_code'  => 'nullable|string|max:10',
            'address'       => 'nullable|string|max:255',
            'city'          => 'nullable|string|max:100',
            'state'         => 'nullable|string|max:100',
            'zip'           => 'nullable|string|max:20',
            'image'         => ['nullable', 'image', new FileTypeValidate(['jpg', 'jpeg', 'png'])]
        ], [
            'full_name.required' => 'Full name is required',
            'full_name.max'      => 'Full name cannot exceed 255 characters',
            'employ_id.max'      => 'Employee ID cannot exceed 50 characters',
            'phone.max'          => 'Phone number cannot exceed 20 characters',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        // Update BCG employee fields
        $user->full_name    = $request->full_name;
        $user->employ_id    = $request->employ_id;
        $user->phone        = $request->phone;
        $user->country_code = $request->country_code;
        $user->address      = $request->address;
        $user->city         = $request->city;
        $user->state        = $request->state;
        $user->zip          = $request->zip;

        // Handle image upload
        if ($request->hasFile('image')) {
            try {
                $user->image = fileUploader($request->image, getFilePath('user'), getFileSize('user'), $user->image);
            } catch (\Exception $exp) {
                $notify[] = 'Couldn\'t upload your image';
                return apiResponse('exception', 'error', $notify);
            }
        }

        $user->save();

        $notify[] = 'BCG employee profile updated successfully';

        // Return clean BCG employee data
        $userData = [
            'id'               => $user->id,
            'email'            => $user->email,
            'employ_type'      => $user->employ_type,
            'full_name'        => $user->full_name,
            'employ_id'        => $user->employ_id,
            'phone'            => $user->phone,
            'country_code'     => $user->country_code,
            'address'          => $user->address,
            'city'             => $user->city,
            'state'            => $user->state,
            'zip'              => $user->zip,
            'ev'               => $user->ev,
            'sv'               => $user->sv,
            'profile_complete' => $user->profile_complete,
            'image_src'        => $user->image_src,
            'updated_at'       => $user->updated_at,
        ];

        return apiResponse("bcg_profile_updated", "success", $notify, [
            'user' => $userData
        ]);
    }

    public function submitPassword(Request $request)
    {
        $passwordValidation = Password::min(6);
        if (gs('secure_password')) {
            $passwordValidation = $passwordValidation->mixedCase()->numbers()->symbols()->uncompromised();
        }

        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'password'         => ['required', 'confirmed', $passwordValidation]
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $user = auth()->user();
        if (Hash::check($request->current_password, $user->password)) {
            $password       = Hash::make($request->password);
            $user->password = $password;
            $user->save();
            $notify[] = 'Password changed successfully';
            return apiResponse("password_changed", "success", $notify);
        } else {
            $notify[] = 'The password doesn\'t match!';
            return apiResponse("not_match", "validation_error", $notify);
        }
    }

    public function addDeviceToken(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'token' => 'required',
        ]);
        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $userId      = auth()->user()->id;
        $deviceToken = DeviceToken::where('token', $request->token)->where('user_id', $userId)->first();

        if ($deviceToken) {
            $notify[] = 'Token already exists';
            return apiResponse("token_exists", "error", $notify);
        }

        $deviceToken          = new DeviceToken();
        $deviceToken->user_id = auth()->user()->id;
        $deviceToken->token   = $request->token;
        $deviceToken->is_app  = Status::YES;
        $deviceToken->save();

        $notify[] = 'Token saved successfully';
        return apiResponse("token_saved", "success", $notify);
    }



    public function pushNotifications()
    {
        $notifications = NotificationLog::where('user_id', auth()->id())->where('sender', 'firebase')->orderBy('id', 'desc')->paginate(getPaginate());
        $notify[]      = 'Push notifications';
        return apiResponse("notifications", "success", $notify, [
            'notifications' => $notifications,
        ]);
    }


    public function pushNotificationsRead($id)
    {
        $notification = NotificationLog::where('user_id', auth()->id())->where('sender', 'firebase')->find($id);
        if (!$notification) {
            $notify[] = 'Notification not found';
            return apiResponse("notification_not_found", "error", $notify);
        }
        $notify[]                = 'Notification marked as read successfully';
        $notification->user_read = 1;
        $notification->save();

        return apiResponse("notification_read", "success", $notify);
    }


    public function userInfo()
    {
        $notify[] = 'User information';
        return apiResponse("user_info", "success", $notify, [
            'user'       => auth()->user(),
            'image_path' => getFilePath('user')
        ]);
    }

    public function deleteAccount()
    {
        $user             = auth()->user();
        $user->is_deleted = Status::YES;
        $user->save();

        $user->tokens()->delete();

        $notify[] = 'Account deleted successfully';
        return apiResponse("account_deleted", "success", $notify);
    }


    public function pusher($socketId, $channelName)
    {
        $general      = gs();
        $pusherSecret = $general->pusher_config->app_secret;
        $str          = $socketId . ":" . $channelName;
        $hash         = hash_hmac('sha256', $str, $pusherSecret);

        return response()->json([
            'auth'    => $general->pusher_config->app_key . ":" . $hash,
        ]);
    }

    public function review($driverId)
    {
        $notify[] = 'Driver Review List';
        return apiResponse("review", "success", $notify, [
            'user_image_path'   => getFilePath('user'),
            'driver_image_path' => getFilePath('driver'),
            "reviews"           => Review::with("user")->latest('id')->where('driver_id', $driverId)->get()
        ]);
    }

    /**
     * Send password reset code
     */
    public function forgotPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email'
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $user = User::where('email', $request->email)->first();
        $code = verificationCode(6);
        $user->ver_code = $code;
        $user->ver_code_send_at = now();
        $user->save();

        $userIpInfo = getIpInfo();
        $userBrowser = osBrowser();
        notify($user, 'PASS_RESET_CODE', [
            'code' => $code,
            'ip' => $userIpInfo['ip'],
            'browser' => $userBrowser['browser'],
            'os' => $userBrowser['os'],
            'time' => now(),
        ], ['email']);

        $notify[] = 'Verification code sent to your email';
        return apiResponse("verification_code_sent", 'success', $notify);
    }

    /**
     * Reset password
     */
    public function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required',
            'email' => 'required|email|exists:users,email',
            'password' => 'required|min:6|confirmed'
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            $notify[] = 'User not found';
            return apiResponse("not_found", 'error', $notify);
        }

        if ($user->ver_code != $request->code) {
            $notify[] = 'Verification code does not match';
            return apiResponse("invalid_code", 'error', $notify);
        }

        if (now()->isAfter($user->ver_code_send_at->addMinutes(10))) {
            $notify[] = 'Verification code expired';
            return apiResponse("code_expired", 'error', $notify);
        }

        $user->password = Hash::make($request->password);
        $user->ver_code = null;
        $user->ver_code_send_at = null;
        $user->save();

        $userIpInfo = getIpInfo();
        $userBrowser = osBrowser();
        notify($user, 'PASS_RESET_DONE', [
            'ip' => $userIpInfo['ip'],
            'browser' => $userBrowser['browser'],
            'os' => $userBrowser['os'],
            'time' => now(),
        ], ['email']);

        $notify[] = 'Password changed successfully';
        return apiResponse("password_changed", 'success', $notify);
    }
}
