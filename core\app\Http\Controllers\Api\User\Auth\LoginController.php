<?php

namespace App\Http\Controllers\Api\User\Auth;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Lib\SocialLogin;
use App\Models\UserLogin;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\PersonalAccessToken;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */

    protected $username;

    /**
     * Create a new controller instance.
     *
     * @return void
     */


    public function __construct()
    {
        // For BCG employees, we always use email for login
        $this->username = 'email';
    }

    public function login(Request $request)
    {
        $validator = $this->validateLogin($request);
        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $credentials = [
            'email'      => $request->email,
            'password'   => $request->password,
            'is_deleted' => Status::NO
        ];

        if (!Auth::attempt($credentials)) {
            $response[] = 'Invalid email or password';
            return apiResponse("invalid_credential", "error", $response);
        }

        $user        = $request->user();
        $tokenResult = $user->createToken('auth_token', ['user'])->plainTextToken;
        $this->authenticated($request, $user);
        $response[] = 'Login Successful';

        // Prepare user data with BCG employee fields
        $userData = [
            'id'               => $user->id,
            'email'            => $user->email,
            'employ_type'      => $user->employ_type,
            'full_name'        => $user->full_name,
            'employ_id'        => $user->employ_id,
            'phone'            => $user->phone,
            'firstname'        => $user->firstname,
            'lastname'         => $user->lastname,
            'username'         => $user->username,
            'mobile'           => $user->mobile,
            'country_code'     => $user->country_code,
            'ev'               => $user->ev,
            'sv'               => $user->sv,
            'profile_complete' => $user->profile_complete,
            'image_src'        => $user->image_src,
            'created_at'       => $user->created_at,
            'updated_at'       => $user->updated_at,
        ];

        return apiResponse("login_success", "success", $response, [
            'user'         => $userData,
            'access_token' => $tokenResult,
            'token_type'   => 'Bearer'
        ]);
    }

    /**
     * BCG Employee Login - Simplified login for BCG employees using email
     */
    public function bcgLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email'    => 'required|email|regex:/^[a-zA-Z0-9._%+-]+@bcg\.com$/',
            'password' => 'required|string',
        ], [
            'email.required' => 'Email address is required',
            'email.email'    => 'Please provide a valid email address',
            'email.regex'    => 'Please use your BCG email address (@bcg.com)',
            'password.required' => 'Password is required',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $credentials = [
            'email'      => $request->email,
            'password'   => $request->password,
            'is_deleted' => Status::NO
        ];

        if (!Auth::attempt($credentials)) {
            $response[] = 'Invalid email or password';
            return apiResponse("invalid_credential", "error", $response);
        }

        $user = Auth::user();
        $tokenResult = $user->createToken('auth_token', ['user'])->plainTextToken;
        $this->authenticated($request, $user);
        $response[] = 'Login Successful';

        // BCG Employee specific response data
        $userData = [
            'id'               => $user->id,
            'email'            => $user->email,
            'employ_type'      => $user->employ_type,
            'full_name'        => $user->full_name,
            'employ_id'        => $user->employ_id,
            'phone'            => $user->phone,
            'ev'               => $user->ev,
            'sv'               => $user->sv,
            'profile_complete' => $user->profile_complete,
            'image_src'        => $user->image_src,
            'created_at'       => $user->created_at,
        ];

        return apiResponse("login_success", "success", $response, [
            'user'         => $userData,
            'access_token' => $tokenResult,
            'token_type'   => 'Bearer'
        ]);
    }

    public function username()
    {
        return $this->username;
    }

    protected function validateLogin(Request $request):object
    {
        $validationRule = [
            'email'    => 'required|email|string',
            'password' => 'required|string',
        ];

        $validate = Validator::make($request->all(), $validationRule, [
            'email.required' => 'Email address is required',
            'email.email'    => 'Please provide a valid email address',
            'password.required' => 'Password is required',
        ]);

        return $validate;
    }

    public function logout()
    {
        auth()->user()->tokens()->delete();
        $notify[] = 'Logout Successful';
        return apiResponse("logout", "success", $notify);
    }

    public function authenticated(Request $request, $user)
    {
        $user->tv = $user->ts == Status::VERIFIED ? Status::UNVERIFIED : Status::VERIFIED;
        $user->save();
        $ip        = getRealIP();
        $exist     = UserLogin::where('user_ip', $ip)->first();
        $userLogin = new UserLogin();
        if ($exist) {
            $userLogin->longitude    = $exist->longitude;
            $userLogin->latitude     = $exist->latitude;
            $userLogin->city         = $exist->city;
            $userLogin->country_code = $exist->country_code;
            $userLogin->country      = $exist->country;
        } else {
            $info                    = json_decode(json_encode(getIpInfo()), true);
            $userLogin->longitude    = @implode(',', $info['long']);
            $userLogin->latitude     = @implode(',', $info['lat']);
            $userLogin->city         = @implode(',', $info['city']);
            $userLogin->country_code = @implode(',', $info['code']);
            $userLogin->country      = @implode(',', $info['country']);
        }

        $userAgent          = osBrowser();
        $userLogin->user_id = $user->id;
        $userLogin->user_ip = $ip;

        $userLogin->browser = @$userAgent['browser'];
        $userLogin->os      = @$userAgent['os_platform'];
        $userLogin->save();
    }

    public function checkToken(Request $request)
    {
        $validationRule = [
            'token' => 'required',
        ];

        $validator = Validator::make($request->all(), $validationRule);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $accessToken = PersonalAccessToken::findToken($request->token);

        if ($accessToken) {
            $notify[]      = 'Token exists';
            $data['token'] = $request->token;
            return apiResponse("token_exists", "success", $notify, $data);
        }

        $notify[] = 'Token doesn\'t exists';
        return apiResponse("token_not_exists", "error", $notify);
    }

    public function socialLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider' => 'required|in:google,apple',
            'token'    => 'required',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $socialLogin = new SocialLogin("user",$request->provider);
        return $socialLogin->login();
    }
}
