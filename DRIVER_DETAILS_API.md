# Driver Details API

## Overview
This document describes the new Driver Details API endpoint that provides comprehensive information about a driver including personal details, vehicle information, statistics, and recent reviews.

## Endpoint Details

### GET `/api/driver/driver-details`

**Description:** Retrieve comprehensive driver information including personal details, vehicle info, statistics, and recent reviews.

**Authentication:** Required (<PERSON><PERSON>)

**Method:** GET

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {your_access_token}
```

## Response Structure

### Success Response (200 OK)

```json
{
    "success": true,
    "message": "Driver details retrieved successfully",
    "data": {
        "driver": {
            "id": 1,
            "personal_info": {
                "firstname": "<PERSON>",
                "lastname": "<PERSON><PERSON>",
                "fullname": "<PERSON>",
                "username": "john_driver",
                "email": "<EMAIL>",
                "mobile": "1234567890",
                "dial_code": "+1",
                "mobile_number": "+11234567890",
                "image": "https://example.com/path/to/image.jpg",
                "address": "123 Main St",
                "city": "New York",
                "state": "NY",
                "zip": "10001",
                "country_name": "United States"
            },
            "professional_info": {
                "balance": "1,250.00",
                "online_status": 1,
                "status": 1,
                "profile_complete": 1,
                "total_reviews": 45,
                "avg_rating": "4.75"
            },
            "verification_status": {
                "email_verified": 1,
                "mobile_verified": 1,
                "document_verified": 1,
                "vehicle_verified": 1,
                "two_factor_enabled": 0
            },
            "service": {
                "id": 1,
                "name": "Car",
                "image": "car_icon.png"
            },
            "zone": {
                "id": 1,
                "name": "Downtown",
                "status": 1
            },
            "vehicle": {
                "id": 1,
                "vehicle_number": "ABC123",
                "model": {
                    "id": 1,
                    "name": "Camry"
                },
                "color": {
                    "id": 1,
                    "name": "White"
                },
                "year": {
                    "id": 1,
                    "name": "2022"
                },
                "brand": {
                    "id": 1,
                    "name": "Toyota"
                },
                "image": "vehicle_image.jpg"
            },
            "statistics": {
                "total_rides": 150,
                "completed_rides": 145,
                "total_earnings": "5,250.00",
                "average_rating": "4.75",
                "total_reviews": 45,
                "completion_rate": "96.67"
            },
            "recent_reviews": [
                {
                    "id": 1,
                    "rating": 5,
                    "comment": "Excellent driver!",
                    "created_at": "2024-01-15 10:30:00",
                    "rider": {
                        "name": "Jane Smith"
                    }
                }
            ],
            "rider_rules": ["No Smoking", "Pet Friendly"],
            "created_at": "2024-01-01 00:00:00",
            "updated_at": "2024-01-15 12:00:00"
        },
        "driver_image_path": "/path/to/driver/images",
        "vehicle_image_path": "/path/to/vehicle/images",
        "service_image_path": "/path/to/service/images",
        "brand_image_path": "/path/to/brand/images"
    }
}
```

### Error Responses

#### 401 Unauthorized
```json
{
    "message": "Unauthenticated."
}
```

#### 404 Not Found
```json
{
    "success": false,
    "message": "Driver not found",
    "data": null
}
```

#### 500 Internal Server Error
```json
{
    "success": false,
    "message": "Failed to retrieve driver details",
    "error": "Error message details"
}
```

## Data Fields Explanation

### Personal Info
- **firstname/lastname**: Driver's name
- **fullname**: Concatenated full name
- **username**: Unique username
- **email**: Driver's email address
- **mobile**: Phone number without country code
- **dial_code**: Country calling code
- **mobile_number**: Complete phone number with country code
- **image**: Profile image URL
- **address/city/state/zip**: Location information
- **country_name**: Country name

### Professional Info
- **balance**: Current wallet balance (formatted)
- **online_status**: 1 = online, 0 = offline
- **status**: 1 = active, 0 = banned
- **profile_complete**: 1 = complete, 0 = incomplete
- **total_reviews**: Number of reviews received
- **avg_rating**: Average rating (formatted to 2 decimal places)

### Verification Status
- **email_verified**: 1 = verified, 0 = unverified
- **mobile_verified**: 1 = verified, 0 = unverified
- **document_verified**: 1 = verified, 0 = unverified, 2 = pending
- **vehicle_verified**: 1 = verified, 0 = unverified, 2 = pending
- **two_factor_enabled**: 1 = enabled, 0 = disabled

### Statistics
- **total_rides**: Total number of rides
- **completed_rides**: Number of completed rides
- **total_earnings**: Total earnings (formatted)
- **average_rating**: Average rating from reviews
- **total_reviews**: Total number of reviews
- **completion_rate**: Percentage of completed rides

### Recent Reviews
- Limited to 10 most recent reviews
- Includes rating, comment, date, and rider name

## Usage Examples

### cURL Example
```bash
curl -X GET \
  'https://your-domain.com/api/driver/driver-details' \
  -H 'Accept: application/json' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_access_token_here'
```

### JavaScript (Fetch) Example
```javascript
fetch('/api/driver/driver-details', {
    method: 'GET',
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + accessToken
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Driver details:', data.data.driver);
    } else {
        console.error('Error:', data.message);
    }
});
```

## Testing

Run the test suite to verify the API functionality:

```bash
php artisan test --filter=DriverDetailsApiTest
```

## Notes

- This endpoint requires driver authentication
- All monetary values are formatted with commas and 2 decimal places
- Image paths are provided separately for easy URL construction
- Recent reviews are limited to the 10 most recent entries
- Statistics are calculated in real-time from the database
- The endpoint handles missing relationships gracefully (returns null)

## Related Endpoints

- `GET /api/driver/driver-info` - Basic driver information
- `GET /api/driver/dashboard` - Driver dashboard data
- `POST /api/driver/profile-setting` - Update driver profile
