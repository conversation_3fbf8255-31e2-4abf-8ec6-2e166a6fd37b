<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rides', function (Blueprint $table) {
            // BCG Corporate ride fields
            $table->string('ride_option')->nullable()->after('status'); // 'go_now' or 'schedule'
            $table->timestamp('scheduled_time')->nullable()->after('ride_option');
            $table->integer('estimated_duration')->nullable()->after('scheduled_time'); // Estimated duration in minutes
            $table->string('case_code')->nullable()->after('estimated_duration'); // BCG Case Code
            $table->json('stops')->nullable()->after('case_code'); // Multiple stops data
            $table->integer('cycles_count')->nullable()->after('amount'); // Number of 30-minute cycles
            $table->decimal('fare_per_cycle', 28, 8)->nullable()->after('cycles_count'); // Fare per 30-minute cycle

            // Remove OTP field if it exists
            $table->dropColumn('otp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rides', function (Blueprint $table) {
            $table->dropColumn([
                'ride_option',
                'scheduled_time',
                'estimated_duration',
                'case_code',
                'stops',
                'cycles_count',
                'fare_per_cycle'
            ]);

            // Add OTP back if rolling back
            $table->string('otp', 10)->nullable();
        });
    }
};
