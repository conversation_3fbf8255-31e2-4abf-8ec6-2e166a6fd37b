<?php

namespace App\Http\Controllers\Api\User;

use App\Models\Ride;
use App\Models\Zone;
use App\Models\Coupon;
use App\Models\Driver;
use App\Models\Service;
use App\Models\SosAlert;
use App\Constants\Status;
use App\Events\Ride as EventsRide;
use Illuminate\Http\Request;
use App\Models\GatewayCurrency;
use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use App\Models\Bid;
use App\Models\Deposit;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Barryvdh\DomPDF\Facade\Pdf;

class RideController extends Controller
{
    /**
     * BCG Corporate Ride System - Get available drivers and fare estimate
     */
    public function getAvailableDrivers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_id'            => 'required|integer',
            'pickup_latitude'       => 'required|numeric',
            'pickup_longitude'      => 'required|numeric',
            'destination_latitude'  => 'nullable|numeric',
            'destination_longitude' => 'nullable|numeric',
            'ride_option'           => 'required|in:go_now,schedule',
            'scheduled_time'        => 'required_if:ride_option,schedule|date|after:+2 hours',
            'stops'                 => 'nullable|array',
            'stops.*.latitude'      => 'required_with:stops|numeric',
            'stops.*.longitude'     => 'required_with:stops|numeric',
            'stops.*.address'       => 'nullable|string',
            'stops.*.note'          => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $service = Service::active()->find($request->service_id);

        if (!$service) {
            $notify[] = 'This service is currently unavailable';
            return apiResponse("not_found", 'error', $notify);
        }

        $zoneData = $this->getZone($request);

        if (@$zoneData['status'] == 'error') {
            $notify[] = $zoneData['message'];
            return apiResponse('not_found', 'error', $notify);
        }

        $pickupZone = $zoneData['pickup_zone'];

        // System automatically finds best available driver (no user selection)
        $availableDriver = Driver::active()
            ->where('online_status', Status::YES)
            ->where('zone_id', $pickupZone->id)
            ->where("service_id", $request->service_id)
            ->where('dv', Status::VERIFIED)
            ->where('vv', Status::VERIFIED)
            ->notRunning()
            ->with(['vehicle', 'vehicle.model', 'vehicle.color', 'vehicle.year'])
            ->orderBy('rating', 'desc') // Best rated driver first
            ->first();

        // Calculate estimated fare based on 30-minute cycles
        $estimatedDuration = 30; // Default 30 minutes for initial estimate
        if ($request->destination_latitude && $request->destination_longitude) {
            $googleMapData = $this->getGoogleMapData($request);
            if (@$googleMapData['status'] != 'error') {
                $estimatedDuration = $googleMapData['duration_minutes'] ?? 30;
            }
        }

        // Calculate 30-minute cycles (minimum 1 cycle)
        $cycles = max(1, ceil($estimatedDuration / 30));
        $farePerCycle = $service->half_hour_fare;
        $estimatedFare = $cycles * $farePerCycle;

        // BCG Corporate fare structure
        $fareData = [
            'estimated_duration_minutes' => $estimatedDuration,
            'cycles_count' => $cycles,
            'fare_per_cycle' => $farePerCycle,
            'estimated_fare' => $estimatedFare,
            'max_duration_hours' => 24,
            'max_cycles' => 48,
            'cancellation_charges' => $service->cancellation_charges,
            'billing_note' => 'Charged per 30-minute cycle. Company pays directly.'
        ];

        if (!$availableDriver) {
            $notify[] = 'No drivers available in your area for the selected service';
            return apiResponse("no_drivers_available", 'error', $notify);
        }

        $notify[] = 'Driver will be automatically assigned by system';
        return apiResponse("driver_available", 'success', $notify, [
            'service_available' => true,
            'fare_data' => $fareData,
            'ride_option' => $request->ride_option,
            'scheduled_time' => $request->scheduled_time ?? null,
            'pickup_zone' => $pickupZone,
            'service_types' => [
                ['id' => 1, 'name' => 'Sedan', 'description' => 'Standard 4-passenger vehicle'],
                ['id' => 2, 'name' => 'SUV', 'description' => 'Premium 4-passenger SUV']
            ]
        ]);
    }

    /**
     * BCG Corporate Ride Creation - Direct driver selection
     */
    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_id'            => 'required|integer',
            'pickup_latitude'       => 'required|numeric',
            'pickup_longitude'      => 'required|numeric',
            'destination_latitude'  => 'nullable|numeric',
            'destination_longitude' => 'nullable|numeric',
            'note'                  => 'nullable|string',
            'number_of_passenger'   => 'required|integer|min:1|max:4', // Maximum 4 passengers
            'ride_option'           => 'required|in:go_now,schedule',
            'scheduled_time'        => 'required_if:ride_option,schedule|date|after:+2 hours', // Minimum 2 hours before
            'estimated_duration'    => 'nullable|integer|min:30|max:1440', // 30 minutes to 24 hours
            'case_code'             => 'required|string|max:50', // BCG Case Code required
            'stops'                 => 'nullable|array', // No limit on stops, default null
            'stops.*.latitude'      => 'required_with:stops|numeric',
            'stops.*.longitude'     => 'required_with:stops|numeric',
            'stops.*.address'       => 'nullable|string|max:255',
            'stops.*.note'          => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        // Check for existing active rides
        $existsRide = Ride::where('user_id', auth()->id())
            ->whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE, Status::RIDE_RUNNING])
            ->exists();

        if ($existsRide) {
            $notify[] = 'You can create a ride after finishing your ongoing ride.';
            return apiResponse("active_ride_exists", 'error', $notify);
        }

        // Validate service
        $service = Service::active()->find($request->service_id);
        if (!$service) {
            $notify[] = 'This service is currently unavailable';
            return apiResponse("not_found", 'error', $notify);
        }

        // System automatically assigns best available driver
        $driver = Driver::active()
            ->where('online_status', Status::YES)
            ->where("service_id", $request->service_id)
            ->where('dv', Status::VERIFIED)
            ->where('vv', Status::VERIFIED)
            ->notRunning()
            ->orderBy('rating', 'desc') // Best rated driver first
            ->first();

        if (!$driver) {
            $notify[] = 'No drivers available for the selected service in your area';
            return apiResponse("no_drivers_available", 'error', $notify);
        }

        // Validate pickup zone
        $zoneData = $this->getZone($request);
        if (@$zoneData['status'] == 'error') {
            $notify[] = $zoneData['message'];
            return apiResponse('not_found', 'error', $notify);
        }

        $pickupZone = $zoneData['pickup_zone'];
        $user = auth()->user();

        // Calculate distance and duration if destination provided
        $distance = 0;
        $duration = 0;
        $destinationAddress = null;
        $originAddress = null;

        if ($request->destination_latitude && $request->destination_longitude) {
            $googleMapData = $this->getGoogleMapData($request);
            if (@$googleMapData['status'] != 'error') {
                $distance = $googleMapData['distance'] ?? 0;
                $duration = $googleMapData['duration_minutes'] ?? 0;
                $destinationAddress = $googleMapData['destination_address'] ?? null;
                $originAddress = $googleMapData['origin_address'] ?? null;
            }
        }

        // BCG Corporate fare calculation - 30-minute cycles
        $estimatedDuration = $request->estimated_duration ?? max(30, $duration);
        $cycles = max(1, ceil($estimatedDuration / 30));
        $farePerCycle = $service->half_hour_fare;
        $estimatedFare = $cycles * $farePerCycle;

        // Create BCG Corporate Ride
        $ride = new Ride();
        $ride->uid                   = getTrx(10);
        $ride->user_id               = $user->id;
        $ride->driver_id             = $driver->id; // Direct driver assignment
        $ride->service_id            = $request->service_id;
        $ride->pickup_location       = $originAddress ?? 'Pickup Location';
        $ride->pickup_latitude       = $request->pickup_latitude;
        $ride->pickup_longitude      = $request->pickup_longitude;
        $ride->destination           = $destinationAddress ?? 'Destination';
        $ride->destination_latitude  = $request->destination_latitude;
        $ride->destination_longitude = $request->destination_longitude;
        $ride->ride_type             = Status::CITY_RIDE; // Default to city ride for BCG
        $ride->note                  = $request->note;
        $ride->number_of_passenger   = $request->number_of_passenger;
        $ride->distance              = $distance;
        $ride->duration              = $estimatedDuration . ' minutes';
        $ride->pickup_zone_id        = $pickupZone->id;
        $ride->destination_zone_id   = $pickupZone->id; // Same zone for now

        // BCG Corporate pricing - 30-minute cycles
        $ride->recommend_amount      = $estimatedFare;
        $ride->min_amount            = $farePerCycle; // Minimum 1 cycle
        $ride->max_amount            = 48 * $farePerCycle; // Maximum 24 hours
        $ride->amount                = $estimatedFare;
        $ride->cycles_count          = $cycles;
        $ride->fare_per_cycle        = $farePerCycle;

        // BCG Corporate payment - Company pays
        $ride->payment_type          = Status::PAYMENT_TYPE_CASH; // Default to cash (company pays)
        $ride->commission_percentage = $service->city_fare_commission;
        $ride->gateway_currency_id   = 0;

        // BCG Corporate ride options
        $ride->ride_option           = $request->ride_option;
        $ride->scheduled_time        = $request->ride_option == 'schedule' ? $request->scheduled_time : null;
        $ride->estimated_duration    = $estimatedDuration;
        $ride->case_code             = $request->case_code; // BCG Case Code
        $ride->stops                 = $request->stops; // Multiple stops (auto-cast to JSON)
        $ride->status                = $request->ride_option == 'go_now' ? Status::RIDE_ACTIVE : Status::RIDE_PENDING;
        // No OTP needed for BCG Corporate rides

        $ride->save();

        // BCG Corporate notification system - Direct driver assignment
        $shortCode = [
            'ride_id'         => $ride->uid,
            'service'         => $service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance,
            'cycles'          => $cycles,
            'fare_per_cycle'  => $farePerCycle,
            'estimated_fare'  => $estimatedFare,
            'ride_option'     => $request->ride_option,
            'employee_name'   => $user->full_name ?? $user->firstname . ' ' . $user->lastname,
            'employee_id'     => $user->employ_id ?? 'N/A'
        ];

        // Load relationships
        $ride->load('user', 'service', 'driver', 'driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year');

        // Send notification to assigned driver
        notify($driver, 'BCG_RIDE_ASSIGNED', $shortCode);

        // Send real-time events
        event(new EventsRide("rider-user-$ride->user_id", "BCG_RIDE_CREATED", [
            'ride' => $ride,
        ]));

        event(new EventsRide("rider-driver-$driver->id", "BCG_RIDE_ASSIGNED", [
            'ride'              => $ride,
            'driver_image_path' => getFilePath('driver'),
            'user_image_path'   => getFilePath('user'),
        ]));

        $notify[] = $request->ride_option == 'go_now'
            ? 'Ride created and driver assigned successfully'
            : 'Ride scheduled successfully';

        return apiResponse('bcg_ride_created', 'success', $notify, [
            'ride' => $ride,
            'driver' => $driver,
            'fare_breakdown' => [
                'cycles' => $cycles,
                'fare_per_cycle' => $farePerCycle,
                'estimated_fare' => $estimatedFare,
                'billing_note' => 'Charged per 30-minute cycle. Company pays directly.'
            ]
        ]);
    }

    public function details($id)
    {
        $ride = Ride::with(['bids', 'userReview', 'driver', 'service', 'driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year'])
            ->where('user_id', auth()->id())
            ->find($id);

        if (!$ride) {
            $notify[] = 'Invalid ride';
            return apiResponse('not_found', 'error', $notify);
        }

        $driverRideCount = Ride::where('driver_id', $ride->driver_id)->where('id', '!=', $ride->id)->where('status', Status::RIDE_COMPLETED)->count();
        $notify[]        = 'Ride Details';

        return apiResponse('ride_details', 'success', $notify, [
            'ride'               => $ride,
            'service_image_path' => getFilePath('service'),
            'brand_image_path'   => getFilePath('brand'),
            'user_image_path'    => getFilePath('user'),
            'driver_image_path'  => getFilePath('driver'),
            'driver_total_ride'  => $driverRideCount
        ]);
    }

    public function cancel(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'cancel_reason' => 'required',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $ride = Ride::whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])->where('user_id', auth()->id())->find($id);

        if (!$ride) {
            $notify[] = 'Ride not found';
            return apiResponse("not_found", 'error', $notify);
        }

        $cancelRideCount = Ride::where('user_id', auth()->id())
            ->where('canceled_user_type', Status::USER)
            ->count();

        if ($cancelRideCount >= gs('user_cancellation_limit')) {
            $notify[] = 'You have already exceeded the cancellation limit for this month';
            return apiResponse("limit_exceeded", 'error', $notify);
        }

        $ride->cancel_reason      = $request->cancel_reason;
        $ride->canceled_user_type = Status::USER;
        $ride->status             = Status::RIDE_CANCELED;
        $ride->cancelled_at       = now();
        $ride->save();

        if ($ride->status == Status::RIDE_ACTIVE) {
            notify($ride->driver, 'CANCEL_RIDE', [
                'ride_id'         => $ride->uid,
                'reason'          => $ride->cancel_reason,
                'amount'          => showAmount($ride->amount, currencyFormat: false),
                'service'         => $ride->service->name,
                'pickup_location' => $ride->pickup_location,
                'destination'     => $ride->destination,
                'duration'        => $ride->duration,
                'distance'        => $ride->distance,
            ]);
        }
        $notify[] = 'Ride canceled successfully';
        return apiResponse("canceled_ride", 'success', $notify);
    }

    public function sos(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'latitude'  => 'required|numeric',
            'longitude' => 'required|numeric',
            'message'   => 'nullable',
        ]);

        if ($validator->fails()) {
            return apiResponse('validation_error', 'error', $validator->errors()->all());
        }

        $ride = Ride::running()->where('user_id', auth()->id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('invalid_ride', 'error', $notify);
        }

        $sosAlert            = new SosAlert();
        $sosAlert->ride_id   = $id;
        $sosAlert->latitude  = $request->latitude;
        $sosAlert->longitude = $request->longitude;
        $sosAlert->message   = $request->message;
        $sosAlert->save();

        $adminNotification            = new AdminNotification();
        $adminNotification->user_id   = $ride->user->id;
        $adminNotification->title     = 'A new SOS Alert has been created, please take action';
        $adminNotification->click_url = urlPath('admin.rides.detail', $ride->id);
        $adminNotification->save();

        $notify[] = 'SOS request successfully';
        return apiResponse("sos_request", "success", $notify);
    }


    public function list()
    {
        $rides = Ride::with(['driver', 'user', 'service', 'driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year'])
            ->filter(['ride_type', 'status'])
            ->where('user_id', auth()->id())
            ->orderBy('id', 'desc')
            ->paginate(getPaginate());

        $notify[]      = "Get the ride list";
        $data['rides'] = $rides;
        return apiResponse("ride_list", 'success', $notify, $data);
    }

    private function getZone($request)
    {
        $zones           = Zone::active()->get();
        $pickupAddress   = ['lat' => $request->pickup_latitude, 'long' => $request->pickup_longitude];
        $pickupZone      = null;
        $destinationZone = null;

        foreach ($zones as $zone) {
            $pickupZone = insideZone($pickupAddress, $zone);
            if ($pickupZone) {
                $pickupZone = $zone;
                break;
            }
        }

        if (!$pickupZone) {
            return [
                'status'  => 'error',
                'message' => 'The pickup location is not inside any of our zones'
            ];
        }

        $destinationAddress = ['lat' => $request->destination_latitude, 'long' => $request->destination_longitude];

        foreach ($zones as $zone) {
            $destinationZone = insideZone($destinationAddress, $zone);

            if ($destinationZone) {
                $destinationZone = $zone;
                break;
            }
        }

        if (!$destinationZone) {
            return [
                'status'  => 'error',
                'message' => 'The destination location is not inside any of our zones'
            ];
        }

        return [
            'pickup_zone'      => $pickupZone,
            'destination_zone' => $destinationZone,
            'status'           => 'success'
        ];
    }
    private function getGoogleMapData($request)
    {
        $apiKey        = gs('google_maps_api');
        $url           = "https://maps.googleapis.com/maps/api/distancematrix/json?origins={$request->pickup_latitude},{$request->pickup_longitude}&destinations={$request->destination_latitude},{$request->destination_longitude}&units=driving&key={$apiKey}";
        $response      = file_get_contents($url);
        $googleMapData = json_decode($response);

        if ($googleMapData->status != 'OK') {
            return [
                'status'  => 'error',
                'message' => 'Something went wrong!'
            ];
        }

        if ($googleMapData->rows[0]->elements[0]->status == 'ZERO_RESULTS') {
            return [
                'status'  => 'error',
                'message' => 'Direction not found'
            ];
        }

        $distance = $googleMapData->rows[0]->elements[0]->distance->value / 1000;
        $duration = $googleMapData->rows[0]->elements[0]->duration->text;
        $durationMinutes = round($googleMapData->rows[0]->elements[0]->duration->value / 60);

        return [
            'distance'            => $distance,
            'duration'            => $duration,
            'duration_minutes'    => $durationMinutes,
            'origin_address'      => $googleMapData->origin_addresses[0],
            'destination_address' => $googleMapData->destination_addresses[0],
        ];
    }

    public function bids($id)
    {
        $ride = Ride::where('user_id', auth()->id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('not_found', 'error', $notify);
        }

        $bids     = Bid::with(['driver', 'driver.service', 'driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year'])->where('ride_id', $ride->id)->whereIn('status', [Status::BID_PENDING, Status::BID_ACCEPTED])->get();
        $notify[] = 'All Bid';

        return apiResponse("bids", "success", $notify, [
            'bids'              => $bids,
            'ride'              => $ride,
            'driver_image_path' => getFilePath('driver'),
            'user_image_path'   => getFilePath('user'),
        ]);
    }

    public function accept($bidId)
    {
        $bid = Bid::pending()->with('ride')->whereHas('ride', function ($q) {
            return $q->pending()->where('user_id', auth()->id());
        })->find($bidId);

        if (!$bid) {
            $notify[] = 'Invalid bid';
            return apiResponse('not_found', 'error', $notify);
        }

        $bid->status      = Status::BID_ACCEPTED;
        $bid->accepted_at = now();
        $bid->save();

        //all the bid rejected after the one accept this bid
        Bid::where('id', '!=', $bid->id)->where('ride_id', $bid->ride_id)->update(['status' => Status::BID_REJECTED]);

        $ride            = $bid->ride;
        $ride->status    = Status::RIDE_ACTIVE;
        $ride->driver_id = $bid->driver_id;
        $ride->otp       = getNumber(6);
        $ride->amount    = $bid->bid_amount;
        $ride->save();

        $ride->load('driver', 'driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year', 'service', 'user');
        $driverRideCount = Ride::where('driver_id', $ride->driver_id)->where('id', '!=', $ride->id)->where('status', Status::RIDE_COMPLETED)->count();

        event(new EventsRide("rider-driver-$ride->driver_id", "BID_ACCEPT", [
            'ride'              => $ride,
            'driver_total_ride' => $driverRideCount
        ]));

        notify($ride->driver, 'ACCEPT_RIDE', [
            'ride_id'         => $ride->uid,
            'amount'          => showAmount($ride->amount),
            'rider'           => $ride->user->username,
            'service'         => $ride->service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance
        ]);

        $notify[] = 'Bid accepted successfully';
        return apiResponse('accepted', 'success', $notify, [
            'ride' => $ride
        ]);
    }

    public function reject($id)
    {
        $bid = Bid::pending()->with('ride', 'driver')->find($id);

        if (!$bid) {
            $notify[] = 'Invalid bid';
            return apiResponse('not_found', 'error', $notify);
        }

        $ride = $bid->ride;
        if ($ride->user_id != auth()->id()) {
            $notify[] = 'This ride is not for this rider';
            return apiResponse('unauthenticated', 'error', $notify);
        }

        $bid->status = Status::BID_REJECTED;
        $bid->save();

        event(new EventsRide("rider-driver-$bid->driver_id", 'BID_REJECT', [
            'ride' => $ride
        ]));

        notify($ride->driver, 'BID_REJECT', [
            'ride_id'         => $ride->uid,
            'amount'          => showAmount($bid->bid_amount),
            'service'         => $ride->service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance
        ]);

        $notify[] = 'Bid rejected successfully';
        return apiResponse('rejected_bid', 'success', $notify);
    }

    public function payment($id)
    {
        $ride = Ride::where('user_id', auth()->id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('not_found', 'error', $notify);
        }

        $ride->load('driver', 'driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year', 'service', 'user', 'coupon');

        $gatewayCurrency = GatewayCurrency::whereHas('method', function ($gate) {
            $gate->active()->automatic();
        })->with('method')->orderby('method_code')->get();

        $notify[] = "Ride Payments";
        return apiResponse('payment', 'success', $notify, [
            'gateways'          => $gatewayCurrency,
            'image_path'        => getFilePath('gateway'),
            'ride'              => $ride,
            'coupons'           => Coupon::orderBy('id', 'desc')->active()->get(),
            'driver_image_path' => getFilePath('driver'),
        ]);
    }

    public function paymentSave(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'payment_type' => ['required', Rule::in(Status::PAYMENT_TYPE_GATEWAY, Status::PAYMENT_TYPE_CASH)],
            'method_code'  => 'required_if:payment_type,1',
            'currency'     => 'required_if:payment_type,1',
            'tips_amount'  => 'required|numeric|gte:0'
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $ride  = Ride::where('user_id', auth()->id())->find($id);

        if (!$ride) {
            $notify[] = 'The ride is not found';
            return apiResponse('not_found', 'error', $notify);
        }

        if ($ride->status == Status::RIDE_COMPLETED) {
            $notify[] = 'The ride is already completed';
            return apiResponse('not_found', 'error', $notify);
        }

        $ride->tips_amount = $request->tips_amount;
        $ride->save();

        if ($request->payment_type == Status::PAYMENT_TYPE_GATEWAY) {
            return $this->paymentViaGateway($request, $ride);
        } else {

            $ride->payment_status = Status::WAITING_FOR_CASH_PAYMENT;
            $ride->save();

            $ride->load('driver.vehicle', 'driver.vehicle.model', 'driver.vehicle.color', 'driver.vehicle.year', 'user', 'service');

            event(new EventsRide("rider-driver-$ride->driver_id", 'CASH_PAYMENT_REQUEST', [
                'ride' => $ride
            ]));

            event(new EventsRide("rider-user-$ride->user_id", 'CASH_PAYMENT_REQUEST', [
                'ride' => $ride
            ]));

            $notify[] = "Please give the driver " . showAmount($ride->amount) . " in cash.";
            return apiResponse('cash_payment', 'success', $notify, [
                'ride' => $ride
            ]);
        }
    }

    private function paymentViaGateway($request, $ride)
    {
        $amount = $ride->amount - $ride->discount_amount + $ride->tips_amount;

        $gateway = GatewayCurrency::whereHas('method', function ($gateway) {
            $gateway->active()->automatic();
        })->where('method_code', $request->method_code)->where('currency', $request->currency)->first();

        if (!$gateway) {
            $notify[] = "Invalid gateway selected";
            return apiResponse('not_found', 'error', $notify);
        }

        if ($gateway->min_amount > $amount) {
            $notify[] = 'Minimum limit for this gateway is ' . showAmount($gateway->min_amount);
            return apiResponse('limit_exists', 'error', $notify);
        }
        if ($gateway->max_amount < $amount) {
            $notify[] = 'Maximum limit for this gateway is ' . showAmount($gateway->max_amount);
            return apiResponse('limit_exists', 'error', $notify);
        }

        $charge      = 0;
        $payable     = $amount + $charge;
        $finalAmount = $payable * $gateway->rate;
        $user        = auth()->user();

        $data                  = new Deposit();
        $data->from_api        = 1;
        $data->user_id         = $user->id;
        $data->method_code     = $gateway->method_code;
        $data->method_currency = strtoupper($gateway->currency);
        $data->amount          = $amount;
        $data->charge          = $charge;
        $data->rate            = $gateway->rate;
        $data->final_amount    = $finalAmount;
        $data->ride_id         = $ride->id;
        $data->btc_amount      = 0;
        $data->btc_wallet      = "";
        $data->success_url     = urlPath('user.deposit.history');
        $data->failed_url      = urlPath('user.deposit.history');
        $data->trx             = getTrx();
        $data->save();

        $notify[] = "Online Payment";

        return apiResponse("gateway_payment", "success", $notify, [
            'deposit'      => $data,
            'redirect_url' => route('deposit.app.confirm', encrypt($data->id))
        ]);
    }
    public function receipt($id)
    {
        $ride     = Ride::with(['user', 'driver'])->where('user_id', auth()->id())->find($id);

        if (!$ride) {
            $notify[] = "The ride is not available";
            return apiResponse('not_exists', 'error', $notify);
        }

        if ($ride->status != Status::RIDE_COMPLETED) {
            $notify[] = "The ride received is not available at the moment";
            return apiResponse('not_exists', 'error', $notify);
        }

        $type = "user";
        $pdf      = Pdf::loadView('admin.rides.pdf', compact('ride', 'type'));
        $fileName = 'ride.pdf';

        return $pdf->stream($fileName);
    }

    /**
     * Get list of available drivers
     */
    public function getAvailableDriversList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_id' => 'required|integer',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $service = Service::active()->find($request->service_id);

        if (!$service) {
            $notify[] = 'This service is currently unavailable';
            return apiResponse("not_found", 'error', $notify);
        }

        // Get zone based on coordinates
        $zones = Zone::active()->get();
        $address = ['lat' => $request->latitude, 'long' => $request->longitude];
        $pickupZone = null;

        foreach ($zones as $zone) {
            if (insideZone($address, $zone)) {
                $pickupZone = $zone;
                break;
            }
        }

        if (!$pickupZone) {
            $notify[] = 'No service available in your area';
            return apiResponse("not_found", 'error', $notify);
        }

        // Get available drivers
        $availableDrivers = Driver::active()
            ->where('online_status', Status::YES)
            ->where('zone_id', $pickupZone->id)
            ->where("service_id", $request->service_id)
            ->where('dv', Status::VERIFIED)
            ->where('vv', Status::VERIFIED)
            ->notRunning()
            ->with(['vehicle', 'vehicle.model', 'vehicle.color', 'vehicle.year'])
            ->orderBy('rating', 'desc')
            ->get();

        if ($availableDrivers->isEmpty()) {
            $notify[] = 'No drivers available in your area for the selected service';
            return apiResponse("no_drivers_available", 'error', $notify);
        }

        $notify[] = 'Available drivers retrieved successfully';
        return apiResponse("drivers_available", 'success', $notify, [
            'drivers' => $availableDrivers,
            'service' => $service,
            'zone' => $pickupZone
        ]);
    }
}
